<template>
  <nut-config-provider :theme-vars="themeVars">
    <div class="app-container">
      <router-view />
    </div>
  </nut-config-provider>
</template>

<script setup>
import { ref } from 'vue'
const themeVars = ref({
  primaryColor: '#f94c30',
  primaryColorEnd: '#f94c30',
})
</script>

<style>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  background-color: #ffffff;
  color: #2a2a35;
}

.app-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.page-content {
  flex: 1;
  overflow-y: auto;
  background-color: #ffffff;
}

.btn-primary {
  background-color: #f94c30;
  border-color: #f94c30;
  color: #ffffff;
}

.btn-primary:hover {
  background-color: #e0431b;
  border-color: #e0431b;
}

.text-primary {
  color: #f94c30;
}

.bg-primary {
  background-color: #f94c30;
}

.bg-secondary {
  background-color: #2a2a35;
}
</style>
