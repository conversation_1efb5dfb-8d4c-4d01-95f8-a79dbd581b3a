import { createApp } from 'vue'
import { createRouter, createWebHistory } from 'vue-router'
import NutUI from '@nutui/nutui'
import '@nutui/nutui/dist/style.css'

// FontAwesome imports
import { library } from '@fortawesome/fontawesome-svg-core'
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome'
import {
  faBars,
  faQuestionCircle,
  faShoppingCart,
  faBox,
  faTruck,
  faCreditCard,
  faClipboardList,
  faMapMarkerAlt,
  faUser,
  faArrowLeft,
  faArrowRight,
  faStore,
  faBell,
  faChevronRight,
  faChevronDown,
  faChevronUp,
  faCamera,
  faCog,
  faEdit,
  faCheck,
  faPlus,
  faTrash,
  faTimes,
  faCheckCircle,
  faBuilding,
  faProjectDiagram,
  faFlask,
  faLock,
  faClock,
  faStar,
  faShieldAlt,
  faComment,
  faCalendar,
  faCube,
  faTimesCircle,
  faShareAlt,
  faShare  
} from '@fortawesome/free-solid-svg-icons'

// Add icons to the library
library.add(
  faBars,
  faQuestionCircle,
  faShoppingCart,
  faBox,
  faTruck,
  faCreditCard,
  faClipboardList,
  faMapMarkerAlt,
  faUser,
  faArrowLeft,
  faArrowRight,
  faStore,
  faBell,
  faChevronRight,
  faChevronDown,
  faChevronUp,
  faCamera,
  faCog,
  faEdit,
  faCheck,
  faPlus,
  faTrash,
  faTimes,
  faCheckCircle,
  faBuilding,
  faProjectDiagram,
  faFlask,
  faLock,
  faClock,
  faStar,
  faShieldAlt,
  faComment,
  faCalendar,
  faCube,
  faTimesCircle,
  faShareAlt,
  faShare  
)

import App from './App.vue'
import router from './router'
import './assets/css/main.css'

const app = createApp(App)
app.use(NutUI)
app.use(router)
// Register FontAwesome component globally
app.component('font-awesome-icon', FontAwesomeIcon)
app.mount('#app')