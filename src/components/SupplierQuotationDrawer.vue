<template>
  <nut-popup :visible="visible" position="bottom" :style="{ height: '90%' }" round closeable close-icon-position="top-right" @close="handleClose" @update:visible="(val) => emit('update:visible', val)">
    <div class="quotation-drawer">
      <!-- 抽屉头部 -->
      <div class="drawer-header">
        <div class="material-info">
          <h3 class="material-name">{{ materialData.name }}</h3>
          <div class="material-details">
            <span class="material-model">{{ materialData.model }}</span>
            <span class="material-divider">|</span>
            <span class="material-brand">{{ materialData.brand }}</span>
            <span class="material-divider">|</span>
            <span class="material-quantity">数量: {{ materialData.quantity }}</span>
          </div>
        </div>
      </div>

      <!-- 报价统计 -->
      <div class="quotation-stats">
        <div class="stat-item">
          <span class="stat-number">{{ quotationStats.total }}</span>
          <span class="stat-label">总供应商</span>
        </div>
        <div class="stat-item">
          <span class="stat-number">{{ quotationStats.quoted }}</span>
          <span class="stat-label">已报价</span>
        </div>
        <div class="stat-item">
          <span class="stat-number">{{ quotationStats.pending }}</span>
          <span class="stat-label">待报价</span>
        </div>
        <div class="stat-item">
          <span class="stat-number">{{ quotationStats.rejected }}</span>
          <span class="stat-label">已拒绝</span>
        </div>
      </div>

      <!-- 报价列表 -->
      <div class="quotation-list">
        <div class="list-header">
          <h4>供应商报价</h4>
          <div class="sort-options">
            <nut-button size="small" @click="sortBy('price')">
              <font-awesome-icon icon="sort-amount-down" />
              按价格排序
            </nut-button>
          </div>
        </div>

        <div class="quotation-items">
          <div v-for="quotation in sortedQuotations" :key="quotation.id" class="quotation-item">
            <!-- 供应商信息 -->
            <div class="supplier-info">
              <div class="supplier-name">
                {{ quotation.supplierName }}
              </div>
              <div class="status-info">
                <span class="status-badge" :class="getStatusClass(quotation.status)">
                  {{ getStatusText(quotation.status) }}
                </span>
              </div>
            </div>

            <!-- 报价信息 -->
            <div class="quotation-details">
              <div class="price-info">
                <div class="unit-price">
                  <span class="price-label">单价：<span class="price-value">¥{{ quotation.unitPrice.toLocaleString() }}</span></span>
                  
                </div>
                <div class="total-price">
                  <span class="price-label">总价：<span class="price-value total">¥{{ quotation.totalPrice.toLocaleString() }}</span></span>
                  
                </div>
              </div>

              <div class="delivery-info">
                <div class="delivery-time">
                  <span>承诺交期: {{ quotation.deliveryDays }}天</span>
                </div>
                <div class="quotation-time">
                  <span>报价时间: {{ formatDate(quotation.quotationTime,'YYYY-MM-DD HH:mm') }}</span>
                </div>
              </div>

              <!-- 备注信息 -->
              <div v-if="quotation.remark" class="remark-info">
                <span>{{ quotation.remark }}</span>
              </div>
            </div>

            <!-- 操作按钮 -->
            <div v-if="quotation.status === 'quoted'" class="quotation-actions">
              <nut-button size="small" type="primary" @click="adoptQuotation(quotation)"> 前往工作台，采纳报价 </nut-button>
            </div>
          </div>

          <!-- 空状态 -->
          <div v-if="quotations.length === 0" class="empty-state">
            <font-awesome-icon icon="inbox" size="3x" />
            <p>暂无供应商报价</p>
          </div>
        </div>
      </div>
    </div>
  </nut-popup>
</template>

<script setup>
import { ref, computed, watch } from 'vue';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  materialData: {
    type: Object,
    default: () => ({}),
  },
});

const emit = defineEmits(['update:visible', 'adopt-quotation', 'contact-supplier']);

// 排序方式
const sortType = ref('price'); // 'price' | 'time' | 'rating'

// 模拟报价数据
const quotations = ref([
  {
    id: 1,
    supplierName: '上海电气设备有限公司',
    supplierRating: 5,
    unitPrice: 2650,
    totalPrice: 5300,
    deliveryDays: 7,
    quotationTime: new Date('2024-01-16T09:30:00'),
    status: 'quoted',
    remark: '原装正品，提供质保证书',
    isBestPrice: true,
  },
  {
    id: 2,
    supplierName: '北京自动化科技公司',
    supplierRating: 4,
    unitPrice: 2800,
    totalPrice: 5600,
    deliveryDays: 5,
    quotationTime: new Date('2024-01-16T14:20:00'),
    status: 'quoted',
    remark: '现货供应，可提供技术支持',
  },
  {
    id: 3,
    supplierName: '深圳工控设备商行',
    supplierRating: 4,
    unitPrice: 2750,
    totalPrice: 5500,
    deliveryDays: 10,
    quotationTime: new Date('2024-01-17T10:15:00'),
    status: 'quoted',
    remark: '批量优惠，支持货到付款',
  },
  {
    id: 4,
    supplierName: '广州机电设备公司',
    supplierRating: 3,
    unitPrice: 0,
    totalPrice: 0,
    deliveryDays: 0,
    quotationTime: null,
    status: 'pending',
    remark: '',
  },
  {
    id: 5,
    supplierName: '天津工业自动化公司',
    supplierRating: 4,
    unitPrice: 0,
    totalPrice: 0,
    deliveryDays: 0,
    quotationTime: new Date('2024-01-17T16:30:00'),
    status: 'rejected',
    remark: '该型号暂时缺货，无法供应',
  },
]);

// 报价统计
const quotationStats = computed(() => {
  const total = quotations.value.length;
  const quoted = quotations.value.filter((q) => q.status === 'quoted').length;
  const pending = quotations.value.filter((q) => q.status === 'pending').length;
  const rejected = quotations.value.filter((q) => q.status === 'rejected').length;

  return { total, quoted, pending, rejected };
});

// 排序后的报价列表
const sortedQuotations = computed(() => {
  const sorted = [...quotations.value];

  if (sortType.value === 'price') {
    return sorted.sort((a, b) => {
      if (a.status !== 'quoted' && b.status !== 'quoted') return 0;
      if (a.status !== 'quoted') return 1;
      if (b.status !== 'quoted') return -1;
      return a.unitPrice - b.unitPrice;
    });
  }

  return sorted;
});

// 方法
const handleClose = () => {
  emit('update:visible', false);
};

const sortBy = (type) => {
  sortType.value = type;
};

const getStatusClass = (status) => {
  const classes = {
    quoted: 'status-quoted',
    pending: 'status-pending',
    rejected: 'status-rejected',
  };
  return classes[status] || '';
};

const getStatusText = (status) => {
  const texts = {
    quoted: '已报价',
    pending: '待报价',
    rejected: '已拒绝',
  };
  return texts[status] || '未知状态';
};

const formatDate = (date) => {
  if (!date) return '-';
  return date.toLocaleDateString('zh-CN', {
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
  });
};

const adoptQuotation = (quotation) => {
  emit('adopt-quotation', quotation);
};

const contactSupplier = (quotation) => {
  emit('contact-supplier', quotation);
};

// 监听材料数据变化，更新最低价标记
watch(
  () => props.materialData,
  () => {
    // 重新计算最低价
    const quotedItems = quotations.value.filter((q) => q.status === 'quoted');
    if (quotedItems.length > 0) {
      const minPrice = Math.min(...quotedItems.map((q) => q.unitPrice));
      quotations.value.forEach((q) => {
        q.isBestPrice = q.status === 'quoted' && q.unitPrice === minPrice;
      });
    }
  },
  { immediate: true }
);
</script>

<style scoped>
.quotation-drawer {
  padding: 0;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.drawer-header {
  padding: 12px 20px;
  border-bottom: 1px solid #f0f0f0;
  background: #fff;
}

.material-name {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0 0 8px 0;
}

.material-details {
  font-size: 14px;
  color: #666;
}

.material-divider {
  margin: 0 8px;
  color: #ddd;
}

.quotation-stats {
  display: flex;
  padding: 12px 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #f0f0f0;
}

.stat-item {
  flex: 1;
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 20px;
  font-weight: 600;
  color: #f94c30;
}

.stat-label {
  font-size: 12px;
  color: #666;
}

.quotation-list {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 20px;
  border-bottom: 1px solid #f0f0f0;
}

.list-header h4 {
  margin: 0;
  font-size: 16px;
  color: #333;
}

.quotation-items {
  flex: 1;
  overflow-y: auto;
  padding: 12px 16px;
  background-color: #f8f9fa;
}

.quotation-item {
  background: #fff;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  transition: all 0.3s ease;
}

.quotation-item.best-price {
  border-color: #52c41a;
  box-shadow: 0 2px 8px rgba(82, 196, 26, 0.15);
}

.supplier-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.supplier-name {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  display: flex;
  align-items: center;
  gap: 8px;
}

.supplier-rating {
  display: flex;
  align-items: center;
  gap: 4px;
}

.supplier-rating .fa-star {
  color: #ddd;
  font-size: 12px;
}

.supplier-rating .fa-star.filled {
  color: #faad14;
}

.rating-text {
  font-size: 12px;
  color: #666;
  margin-left: 4px;
}

.quotation-details {
  margin-bottom: 12px;
}

.price-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.unit-price,
.total-price {
  text-align: center;
}

.price-label {
  display: block;
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.price-value {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.price-value.total {
  color: #f94c30;
}

.delivery-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 12px;
  color: #666;
}

.delivery-time,
.quotation-time {
  display: flex;
  align-items: center;
  gap: 4px;
}

.status-info {
  margin-bottom: 8px;
}

.status-badge {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.status-quoted {
  background: #f6ffed;
  color: #52c41a;
  border: 1px solid #b7eb8f;
}

.status-pending {
  background: #fff7e6;
  color: #fa8c16;
  border: 1px solid #ffd591;
}

.status-rejected {
  background: #fff2f0;
  color: #ff4d4f;
  border: 1px solid #ffb3b3;
}

.remark-info {
  font-size: 11px;
  color: #666;
  background: rgba(249, 76, 48, 0.05);
  padding: 4px 8px;
  border-radius: 4px;
  border-left: 2px solid #f94c30;
  line-height: 1.3;
}

.quotation-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #999;
}

.empty-state .fa-inbox {
  color: #ddd;
  margin-bottom: 16px;
}
</style>
