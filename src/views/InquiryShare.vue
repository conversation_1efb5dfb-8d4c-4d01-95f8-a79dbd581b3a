<template>
  <div class="inquiry-share-container">
    <!-- Header -->
    <div class="header">
      <div class="header-content">
        <div class="logo-section">
          <div class="company-logo-text">研选LOGO</div>
        </div>
        <div class="share-title">
          <h2>询价单</h2>
        </div>
        <span></span>
      </div>
    </div>

    <!-- 主要内容 -->
    <div class="main-content">
 
      <!-- 询价单基本信息 -->
      <div class="inquiry-basic-info">
        <div class="section-header" @click="toggleBasicInfo">
          <font-awesome-icon icon="file-invoice" class="section-icon" />
          <h4>基本信息</h4>
          <font-awesome-icon
            :icon="basicInfoCollapsed ? 'chevron-down' : 'chevron-up'"
            class="collapse-icon"
          />
        </div>

        <div class="info-content" v-show="!basicInfoCollapsed">
          <!-- 企业信息特殊区域 -->
          <div class="company-info-item">
            <div class="company-logo">
              <div class="logo-placeholder">LOGO</div>
            </div>
            <div class="company-details">
              <div class="company-name">{{ inquiryData.companyName }}</div>
              <div class="inquiry-number">询价单号：{{ inquiryData.number }}</div>
            </div>
          </div>

          <!-- 其他信息网格 -->
          <div class="info-grid">
            <div class="info-item">
              <label>询价时间</label>
              <span class="value">{{ formatDate(inquiryData.createdTime) }}</span>
            </div>
            <div class="info-item">
              <label>截止时间</label>
              <span class="value">{{ formatDate(inquiryData.deadline) }}</span>
            </div>
            <div class="info-item">
              <label>询价人</label>
              <span class="value">{{ inquiryData.inquirer }}</span>
            </div>
            <div class="info-item">
              <label>联系电话</label>
              <span class="value">{{ inquiryData.phone }}</span>
            </div>
            <div class="info-item">
              <label>物料型号数</label>
              <span class="value">{{ inquiryData.materialTypeCount }} 种</span>
            </div>
            <div class="info-item">
              <label>已采纳总价</label>
              <span class="value highlight">¥{{ inquiryData.adoptedTotalAmount.toLocaleString() }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 物料明细 -->
      <div class="material-details">
        <div class="section-header">
          <font-awesome-icon icon="boxes" class="section-icon" />
          <h4>物料明细</h4>
          <span class="material-count">共 {{ materials.length }} 项物料</span>
        </div>

        <div class="materials-list">
          <div
            v-for="material in materials"
            :key="material.id"
            class="material-item"
            @click="showSupplierQuotations(material)"
          >
            <!-- 第一行：物料名称 + 询价状态 -->
            <div class="material-line-1">
              <h4 class="material-name">{{ material.name }}</h4>
              <span
                class="status-badge"
                :style="{ backgroundColor: getStatusColor(material.status) }"
              >
                {{ getStatusText(material.status) }}
              </span>
            </div>

            <!-- 第二行：型号 + 数量 -->
            <div class="material-line-2">
              <span class="material-model">{{ material.model }}</span>
              <span class="material-quantity">x{{ material.quantity }}</span>
            </div>

            <!-- 第三行：品牌、分类 -->
            <div class="material-line-3">
              <nut-tag class="material-brand" type="danger">{{ material.brand }}</nut-tag>
              <nut-tag class="material-category" type="success">{{ material.category }}</nut-tag>
            </div>

            <!-- 第四行：期望交期、接受平替 -->
            <div class="material-line-4">
              <span class="expected-date">期望交期：{{ formatExpectedDate(material.expectedDate) }}</span>
              <span class="accept-alternative" :class="{ 'yes': material.acceptAlternative }">
                {{ material.acceptAlternative ? '接受平替' : '不接受平替' }}
              </span>
            </div>

            <!-- 第五行：备注 -->
            <div v-if="material.remark" class="material-line-5">
              <div class="material-remark">{{ material.remark }}</div>
            </div>

            <!-- 已采纳物料的详细信息 -->
            <div v-if="material.status === 'adopted'" class="accept-section">
              <div class="accept-header">
                <font-awesome-icon icon="check-circle" class="accept-icon" />
                <span class="accept-title">已采纳报价</span>
              </div>

              <div class="accept-details">
                <!-- 价格信息 -->
                <div class="price-info">
                  <div class="price-item">
                    <label>单价</label>
                    <span class="price-value">¥{{ material.unitPrice?.toLocaleString() || '-' }}</span>
                  </div>
                  <div class="price-item">
                    <label>总价</label>
                    <span class="price-value total">¥{{ material.totalPrice?.toLocaleString() || '-' }}</span>
                  </div>
                </div>

                <!-- 交期信息 -->
                <div class="delivery-info">
                  <label>交期</label>
                  <span class="delivery-value">{{ formatDate(material.deliveryDate, 'YYYY-MM-DD') || '-' }}</span>
                </div>

                <!-- 平替信息（仅当接受平替且有平替信息时显示） -->
                <div v-if="material.acceptAlternative && (material.alternativeBrand || material.alternativeModel)" class="alternative-info">
                  <div class="alternative-item">
                    <label>平替品牌</label>
                    <span class="alternative-value">{{ material.alternativeBrand || '-' }}</span>
                  </div>
                  <div class="alternative-item">
                    <label>平替型号</label>
                    <span class="alternative-value">{{ material.alternativeModel || '-' }}</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- 第六行：报价进度 -->
            <div class="material-line-6">
              <span class="quote-progress-text">
                供应商数：{{ material.supplierCount  }}
                已报价：<span class="success-count">{{ material.quotedCount }}  </span>
                已拒绝：<span class="rejected-count">{{ material.rejectedCount }}</span>
              </span>
              <span class="view-quotes-btn">
                查看
                <font-awesome-icon icon="chevron-right" />
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 登录提示弹窗 -->
    <nut-popup v-model:visible="loginPromptVisible" position="center" round>
      <div class="login-prompt">
        <div class="prompt-header">
          <font-awesome-icon icon="lock" />
          <h3>需要登录</h3>
        </div>
        <div class="prompt-content">
          <p>登录后即可查看供应商报价详情</p>
        </div>
        <div class="prompt-actions">
          <button class="prompt-btn login" @click="goToLogin">
            手机号一键登录
          </button>
        </div>
      </div>
    </nut-popup>

    <!-- 供应商报价抽屉 -->
    <SupplierQuotationDrawer
      v-model:visible="quotationDrawerVisible"
      :material-data="selectedMaterial"
      @adopt-quotation="handleAdoptQuotation"
      @contact-supplier="handleContactSupplier"
    />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import SupplierQuotationDrawer from '../components/SupplierQuotationDrawer.vue'

const route = useRoute()

// 响应式数据
const isLoggedIn = ref(false)
const loginPromptVisible = ref(false)
const basicInfoCollapsed = ref(false)

// 供应商报价抽屉相关
const quotationDrawerVisible = ref(false)
const selectedMaterial = ref({})

// 询价单数据
const inquiryData = ref({
  id: 1,
  number: 'RFQ202401001',
  companyName: '上海电气自动化研究所',
  createdTime: new Date('2024-01-15T10:30:00'),
  deadline: new Date('2024-01-22T18:00:00'),
  inquirer: '张工程师',
  phone: '13812348888',
  materialTypeCount: 3,
  adoptedTotalAmount: 8600
})

// 物料数据
const materials = ref([
  {
    id: 1,
    name: '西门子PLC控制器',
    model: '6ES7 214-1BD23-0XB0',
    brand: '西门子',
    category: '控制系统',
    quantity: 2,
    expectedDate: new Date('2026-04-15'),
    acceptAlternative: false,
    status: 'inquiring',
    supplierCount: 5,
    quotedCount: 3,
    rejectedCount: 1,
    remark: '需要原装正品，有CE认证'
  },
  {
    id: 2,
    name: '施耐德接触器',
    model: 'LC1D09M7',
    brand: '施耐德',
    category: '电气元件',
    quantity: 5,
    expectedDate: new Date('2024-04-20'),
    acceptAlternative: true,
    status: 'adopted',
    supplierCount: 4,
    quotedCount: 4,
    rejectedCount: 0,
    remark: '可接受正泰等品牌平替',
    // 已采纳物料的额外字段
    unitPrice: 180,
    totalPrice: 900, // unitPrice * quantity
    deliveryDate: new Date('2024-04-20'),
    alternativeBrand: '正泰',
    alternativeModel: 'NC1-0910'
  },
  {
    id: 3,
    name: 'ABB变频器',
    model: 'ACS550-01-03A3-4',
    brand: 'ABB',
    category: '传动设备',
    quantity: 1,
    expectedDate: new Date('2024-04-25'),
    acceptAlternative: false,
    status: 'inquiring',
    supplierCount: 3,
    quotedCount: 2,
    rejectedCount: 1,
    remark: '需要配套技术支持'
  }
])

// 方法
const formatDate = (date, format = 'YYYY-MM-DD HH:mm') => {
  if (!date) return ''
  const d = new Date(date)

  if (format === 'YYYY-MM-DD') {
    return d.getFullYear() + '-' +
           String(d.getMonth() + 1).padStart(2, '0') + '-' +
           String(d.getDate()).padStart(2, '0')
  }

  if (format === 'MM-DD') {
    return String(d.getMonth() + 1).padStart(2, '0') + '-' +
           String(d.getDate()).padStart(2, '0')
  }

  return d.getFullYear() + '-' +
         String(d.getMonth() + 1).padStart(2, '0') + '-' +
         String(d.getDate()).padStart(2, '0') + ' ' +
         String(d.getHours()).padStart(2, '0') + ':' +
         String(d.getMinutes()).padStart(2, '0')
}

// 格式化期望交期为"n日发货"的形式
const formatExpectedDate = (date) => {
  if (!date) return ''
  const d = new Date(date)
  const now = new Date()

  // 计算距离今天的天数
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
  const targetDate = new Date(d.getFullYear(), d.getMonth(), d.getDate())
  const diffTime = targetDate.getTime() - today.getTime()
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

  if (diffDays < 0) {
    return `已过期${Math.abs(diffDays)}天`
  } else if (diffDays === 0) {
    return '今日发货'
  } else if (diffDays === 1) {
    return '明日发货'
  } else {
    return `${diffDays}日发货`
  }
}

const getStatusColor = (status) => {
  const statusMap = {
    'inquiring': '#1890ff',
    'adopted': '#52c41a',
    'cancelled': '#d9d9d9',
    'expired': '#ff4d4f'
  }
  return statusMap[status] || '#d9d9d9'
}

const getStatusText = (status) => {
  const statusMap = {
    'inquiring': '询价中',
    'adopted': '已采纳',
    'cancelled': '已取消',
    'expired': '已截止'
  }
  return statusMap[status] || '未知'
}

const showLoginPrompt = () => {
  loginPromptVisible.value = true
}

const goToLogin = () => {
  loginPromptVisible.value = false
  // 直接模拟登录成功
  isLoggedIn.value = true
}

const toggleBasicInfo = () => {
  basicInfoCollapsed.value = !basicInfoCollapsed.value
}

// 供应商报价抽屉相关方法
const showSupplierQuotations = (material) => {
  if (!isLoggedIn.value) {
    // 如果未登录，显示登录提示
    loginPromptVisible.value = true
    return
  }

  selectedMaterial.value = material
  quotationDrawerVisible.value = true
}

const handleAdoptQuotation = (quotation) => {
  console.log('采纳报价:', quotation)
  alert(`已采纳 ${quotation.supplierName} 的报价：¥${quotation.totalPrice.toLocaleString()}`)
}

const handleContactSupplier = (quotation) => {
  console.log('联系供应商:', quotation)
  alert(`正在联系供应商：${quotation.supplierName}`)
}

onMounted(() => {
  const inquiryNumber = route.params.inquiryNumber
  console.log('加载询价单数据:', inquiryNumber)
})
</script>

<style scoped>
.inquiry-share-container {
  min-height: 100vh;
  background-color: #f8f9fa;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Header样式 */
.header {
  background: #1a1a2b;
  color: white;
  padding: 12px;
  box-shadow: 0 4px 12px rgba(249, 76, 48, 0.3);
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 16px;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.company-logo-text {
  width: 120px;
  height: 36px;
  border-radius: 8px;
  background: white;
  color: #f94c30;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  font-weight: 700;
}

.company-info {
  display: flex;
  flex-direction: column;
}

.company-name {
  font-size: 24px;
  font-weight: 700;
  margin: 0;
  line-height: 1.2;
}

.company-subtitle {
  font-size: 14px;
  opacity: 0.9;
  margin: 0;
  line-height: 1.2;
}

.share-title h2 {
  font-size: 20px;
  font-weight: 600;
  margin: 0;
}

/* 主要内容 */
.main-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px 16px;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* 通用section样式 */
.inquiry-basic-info,
.material-details {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.section-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
  padding-bottom: 12px;
  border-bottom: 2px solid #f0f0f0;
  cursor: pointer;
  transition: all 0.2s ease;
}

.section-icon {
  font-size: 20px;
  color: #f94c30;
}

.section-header h4 {
  font-size: 16px;
  font-weight: 600;
  color: #2a2a35;
  margin: 0;
  flex: 1;
}

.collapse-icon {
  font-size: 16px;
  color: #666;
  transition: transform 0.2s ease;
}

.material-count {
  font-size: 14px;
  color: #666;
  background: #f8f9fa;
  padding: 4px 12px;
  border-radius: 16px;
}

/* 询价单基本信息 */
.info-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.company-info-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background: linear-gradient(135deg, #f8f9fa, #ffffff);
  border-radius: 8px;
  border: 1px solid #e8e8e8;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.company-logo {
  flex-shrink: 0;
}

.logo-placeholder {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #f94c30, #e0431b);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
  font-weight: 600;
  letter-spacing: 1px;
}

.company-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.company-details .company-name {
  font-size: 16px;
  font-weight: 600;
  color: #2a2a35;
  line-height: 1.3;
}

.inquiry-number {
  font-size: 12px;
  color: #666;
  font-weight: 500;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 4px;
  border-left: 3px solid #f94c30;
}

.info-item label {
  font-size: 12px;
  color: #666;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.info-item .value {
  font-size: 13px;
  color: #2a2a35;
  font-weight: 500;
}

.info-item .value.highlight  {
  color: #f94c30;
  font-size: 16px;
  font-weight: 600;
}

/* 物料列表 */
.materials-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.material-item {
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  padding: 16px;
  background: #fafafa;
  transition: all 0.2s ease;
  cursor: pointer;
}

.material-item:hover {
  border-color: #f94c30;
  box-shadow: 0 2px 8px rgba(249, 76, 48, 0.1);
}

/* 第一行：物料名称 + 询价状态 */
.material-line-1 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.material-name {
  font-size: 14px;
  font-weight: 600;
  color: #2a2a35;
  margin: 0;
  line-height: 1.3;
  flex: 1;
  margin-right: 8px;
}

.status-badge {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 500;
  color: white;
  flex-shrink: 0;
}

/* 第二行：型号 + 数量 */
.material-line-2 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.material-model {
  font-size: 12px;
  color: #2a2a35;
  font-weight: 500;
  flex: 1;
}

.material-quantity {
  font-size: 12px;
  font-weight: 600;
  background: rgba(133, 133, 133, 0.1);
  padding: 2px 6px;
  border-radius: 8px;
}

/* 第三行：品牌、分类 */
.material-line-3 {
  display: flex;
  gap: 12px;
  margin-bottom: 8px;
}

.material-brand {
  font-size: 11px;
  font-weight: 500;
}

.material-category {
  font-size: 11px;
  font-weight: 500;
}

/* 第四行：期望交期、接受平替 */
.material-line-4 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.expected-date {
  font-size: 11px;
  color: #666;
}

.accept-alternative {
  font-size: 10px;
  color: #ff4d4f;
  padding: 1px 6px;
  border-radius: 8px;
  background: rgba(255, 77, 79, 0.1);
}

.accept-alternative.yes {
  color: #27ae60;
  background: rgba(39, 174, 96, 0.1);
}

/* 第五行：备注 */
.material-line-5 {
  margin-bottom: 8px;
}

.material-remark {
  font-size: 11px;
  color: #666;
  background: rgba(249, 76, 48, 0.05);
  padding: 4px 8px;
  border-radius: 4px;
  border-left: 2px solid #f94c30;
  line-height: 1.3;
}

/* 已采纳物料详细信息 */
.accept-section {
  margin-bottom: 8px;
  background: linear-gradient(135deg, rgba(82, 196, 26, 0.05), rgba(82, 196, 26, 0.02));
  border: 1px solid rgba(82, 196, 26, 0.2);
  border-radius: 6px;
  padding: 12px;
}

.accept-header {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 8px;
  padding-bottom: 6px;
  border-bottom: 1px solid rgba(82, 196, 26, 0.2);
}

.accept-icon {
  font-size: 12px;
  color: #52c41a;
}

.accept-title {
  font-size: 11px;
  font-weight: 600;
  color: #52c41a;
}

.accept-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.price-info {
  display: flex;
  gap: 16px;
}

.price-item {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.price-item label {
  font-size: 10px;
  color: #666;
  font-weight: 500;
}

.price-value {
  font-size: 12px;
  font-weight: 600;
  color: #2a2a35;
}

.price-value.total {
  color: #52c41a;
  font-size: 13px;
}

.delivery-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.delivery-info label {
  font-size: 10px;
  color: #666;
  font-weight: 500;
}

.delivery-value {
  font-size: 12px;
  font-weight: 500;
  color: #2a2a35;
}

.alternative-info {
  display: flex;
  gap: 16px;
  padding-top: 6px;
  border-top: 1px solid rgba(82, 196, 26, 0.1);
}

.alternative-item {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.alternative-item label {
  font-size: 10px;
  color: #666;
  font-weight: 500;
}

.alternative-value {
  font-size: 12px;
  font-weight: 500;
  color: #2a2a35;
}

/* 第六行：报价进度 */
.material-line-6 {
  padding-top: 4px;
  border-top: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.quote-progress-text {
  font-size: 11px;
  color: #999;
  font-weight: 500;
}

.success-count {
  color: #52c41a;
}

.rejected-count {
  color: #ff4d4f;
  margin-left: 4px;
}

.view-quotes-btn {
  font-size: 11px;
  color: #999;
  font-weight: 500;
  cursor: pointer;
}

/* 登录提示弹窗 */
.login-prompt {
  padding: 24px;
  text-align: center;
  max-width: 320px;
}

.prompt-header {
  margin-bottom: 16px;
}

.prompt-header svg {
  font-size: 32px;
  color: #f94c30;
  margin-bottom: 8px;
}

.prompt-header h3 {
  font-size: 16px;
  font-weight: 600;
  color: #2a2a35;
  margin: 0;
}

.prompt-content {
  margin-bottom: 20px;
}

.prompt-content p {
  font-size: 14px;
  color: #666;
  line-height: 1.4;
  margin: 0;
}

.prompt-actions {
  display: flex;
  gap: 12px;
}

.prompt-btn {
  flex: 1;
  padding: 10px 16px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  background: white;
  color: #666;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.prompt-btn.cancel {
  border-color: #d9d9d9;
  color: #999;
}

.prompt-btn.cancel:hover {
  background: #f5f5f5;
}

.prompt-btn.login {
  background: linear-gradient(135deg, #f94c30, #e0431b);
  border-color: #f94c30;
  color: white;
}

.prompt-btn.login:hover {
  box-shadow: 0 2px 8px rgba(249, 76, 48, 0.3);
}

/* 响应式设计 */
@media (max-width: 768px) {

  .material-quantity {
    align-self: flex-end;
  }

  .accept-alternative {
    align-self: flex-end;
  }

  .prompt-actions {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .main-content {
    padding: 16px 12px;
  }

  .inquiry-basic-info,
  .material-details {
    padding: 16px;
  }

}
</style>
