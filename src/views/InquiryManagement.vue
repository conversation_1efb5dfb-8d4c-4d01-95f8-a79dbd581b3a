<template>
  <div class="inquiry-management-container">
    <!-- 顶部导航栏 -->
    <div class="header">
      <div class="header-left">
        <font-awesome-icon 
          icon="arrow-left" 
          size="lg" 
          color="#2a2a35" 
          @click="goBack"
          class="back-icon"
        />
      </div>
      
      <div class="header-center">
        <h2 class="page-title">询价管理</h2>
      </div>
      
      <div class="header-right">
        <font-awesome-icon 
          icon="plus" 
          size="lg" 
          color="#f94c30" 
          @click="goToInquiry"
          class="add-icon"
        />
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 搜索栏 -->
      <div class="search-section">
        <div class="search-bar-row">
          <div class="search-bar">
            <font-awesome-icon icon="search" class="search-icon" />
            <input 
              v-model="searchKeyword" 
              type="text" 
              placeholder="搜索物料型号、品牌、询价单号..."
              class="search-input"
              @input="handleSearch"
            />
            <button v-if="searchKeyword" @click="clearSearch" class="clear-btn">
              <font-awesome-icon icon="times" />
            </button>
          </div>
          
          <button 
            @click="showAdvancedFilters = !showAdvancedFilters" 
            class="advanced-filter-toggle"
          >
            <font-awesome-icon icon="filter" />
            <span>高级筛选</span>
            <font-awesome-icon 
              :icon="showAdvancedFilters ? 'chevron-up' : 'chevron-down'" 
              class="toggle-icon"
            />
          </button>
        </div>
        
        <!-- 高级筛选 -->
        <div class="advanced-filters" :class="{ expanded: showAdvancedFilters }">
          <div class="filter-row">
            <div class="filter-group">
              <label>物料型号</label>
              <input v-model="filters.model" type="text" placeholder="输入型号" />
            </div>
            <div class="filter-group">
              <label>品牌</label>
              <input v-model="filters.brand" type="text" placeholder="输入品牌" />
            </div>
            <div class="filter-group">
              <label>接受平替</label>
              <select v-model="filters.acceptAlternative">
                <option value="">全部</option>
                <option value="true">是</option>
                <option value="false">否</option>
              </select>
            </div>
          </div>
          <div class="filter-row">
            <div class="filter-group">
              <label>询价单号</label>
              <input v-model="filters.inquiryNumber" type="text" placeholder="输入单号" />
            </div>
            <div class="filter-group">
              <label>询价时间</label>
              <input v-model="filters.inquiryTimeStart" type="date" />
              <span class="date-separator">至</span>
              <input v-model="filters.inquiryTimeEnd" type="date" />
            </div>
          </div>
          <div class="filter-row">
            <div class="filter-group">
              <label>截止时间</label>
              <input v-model="filters.deadlineStart" type="date" />
              <span class="date-separator">至</span>
              <input v-model="filters.deadlineEnd" type="date" />
            </div>
            <div class="filter-actions">
              <button @click="resetFilters" class="filter-btn reset">
                <font-awesome-icon icon="undo" />
                重置
              </button>
              <button @click="applyFilters" class="filter-btn apply">
                <font-awesome-icon icon="search" />
                应用筛选
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 视图切换和筛选 -->
      <div class="control-section">
        <div class="view-tabs">
          <div 
            class="view-tab" 
            :class="{ active: currentView === 'order' }"
            @click="currentView = 'order'"
          >
            <font-awesome-icon icon="file-invoice" />
            <span>单据视图</span>
          </div>
          <div 
            class="view-tab" 
            :class="{ active: currentView === 'material' }"
            @click="currentView = 'material'"
          >
            <font-awesome-icon icon="boxes" />
            <span>物料视图</span>
          </div>
        </div>
        
        <!-- 物料视图下的状态筛选 -->
        <div v-if="currentView === 'material'" class="filter-section">
          <div class="status-filter">
            <div 
              v-for="status in materialStatusOptions" 
              :key="status.value"
              class="status-chip"
              :class="{ active: selectedStatus === status.value }"
              @click="selectedStatus = selectedStatus === status.value ? null : status.value"
            >
              <span class="status-dot" :style="{ backgroundColor: status.color }"></span>
              <span>{{ status.label }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 单据视图 -->
      <div v-if="currentView === 'order'" class="order-view">
        <div v-if="filteredOrders.length === 0" class="empty-state">
          <div class="empty-icon">
            <font-awesome-icon icon="inbox" size="3x" />
          </div>
          <h3>暂无询价单据</h3>
          <p>还没有创建任何询价单据</p>
          <button class="create-btn" @click="goToInquiry">
            <font-awesome-icon icon="plus" />
            创建询价单
          </button>
        </div>

        <div v-else class="orders-list">
          <div 
            v-for="order in filteredOrders" 
            :key="order.id" 
            class="order-item"
          >
            <div class="order-header">
              <div class="order-info">
                <h3 class="order-number">询价单 #{{ order.number }}</h3>
                <div class="order-meta">
                  <span class="order-time">{{ formatDate(order.createdTime) }}</span>
                  <span class="order-divider">|</span>
                  <span class="order-deadline">截止：{{ formatDate(order.deadline) }}</span>
                </div>
              </div>
              <div class="order-status">
                <!-- 单据没有状态，移除状态显示 -->
              </div>
            </div>
            
            <div class="order-details">
              <div class="detail-tags">
                <div class="detail-tag">
                  <font-awesome-icon icon="layer-group" />
                  <span>{{ order.materialTypeCount }} 种物料</span>
                </div>
                <div class="detail-tag">
                  <font-awesome-icon icon="cubes" />
                  <span>{{ order.totalQuantity }} 件</span>
                </div>
                <div class="detail-tag">
                  <font-awesome-icon icon="yuan-sign" />
                  <span>¥{{ order.totalAmount.toLocaleString() }}</span>
                </div>
              </div>
            </div>

            <div class="order-actions">
              <div class="action-buttons">
                <button 
                  class="action-btn primary"
                  @click="convertToPurchase(order)"
                >
                  <font-awesome-icon icon="shopping-cart" />
                  转采购单
                </button>
                <button class="action-btn" @click="shareOrder(order)">
                  <font-awesome-icon icon="share" />
                  分享
                </button>
                <button class="action-btn" @click="inviteQuote(order)">
                  <font-awesome-icon icon="user-plus" />
                  邀请报价
                </button>
              </div>
              <div class="action-buttons secondary">
                <button class="action-btn edit" @click="editOrder(order)">
                  <font-awesome-icon icon="edit" />
                </button>
                <button class="action-btn delete" @click="deleteOrder(order)">
                  <font-awesome-icon icon="trash" />
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 物料视图 -->
      <div v-if="currentView === 'material'" class="material-view">
        <div v-if="filteredMaterials.length === 0" class="empty-state">
          <div class="empty-icon">
            <font-awesome-icon icon="inbox" size="3x" />
          </div>
          <h3>暂无询价物料</h3>
          <p>还没有任何物料在询价中</p>
          <button class="create-btn" @click="goToInquiry">
            <font-awesome-icon icon="plus" />
            创建询价单
          </button>
        </div>

        <div v-else class="materials-list">
          <div
            v-for="material in filteredMaterials"
            :key="material.id"
            class="material-item"
            @click="showSupplierQuotations(material)"
          >
            <div class="material-header">
              <div class="material-info">
                <h3 class="material-name">{{ material.name }}</h3>
                <div class="material-meta">
                  <span class="material-model">{{ material.model }}</span>
                  <span class="material-divider">|</span>
                  <span class="material-brand">{{ material.brand }}</span>
                  <span class="material-divider">|</span>
                  <span class="material-category">{{ material.category }}</span>
                </div>
              </div>
              <div class="material-status">
                <span
                  class="status-badge small"
                  :style="{ backgroundColor: getStatusColor(material.status) }"
                >
                  {{ getStatusText(material.status) }}
                </span>
              </div>
            </div>
            
            <div class="material-details">
              <div class="detail-row">
                <div class="detail-tags">
                  <div class="detail-tag">
                    <font-awesome-icon icon="calendar" />
                    <span>{{ formatDate(material.expectedDate, 'YYYY-MM-DD') }}</span>
                  </div>
                  <div class="detail-tag">
                    <font-awesome-icon icon="cube" />
                    <span>{{ material.quantity }} 件</span>
                  </div>
                  <div class="detail-tag">
                    <font-awesome-icon icon="yuan-sign" />
                    <span>¥{{ material.unitPrice }}/件</span>
                  </div>
                  <div class="detail-tag total-price">
                    <font-awesome-icon icon="calculator" />
                    <span>总价：¥{{ (material.unitPrice * material.quantity).toLocaleString() }}</span>
                  </div>
                </div>
              </div>

              <div class="detail-row">
                <div class="detail-tags">
                  <div class="detail-tag" :class="{ 'alternative': material.acceptAlternative }">
                    <font-awesome-icon :icon="material.acceptAlternative ? 'check-circle' : 'times-circle'" />
                    <span>{{ material.acceptAlternative ? '接受平替' : '不接受平替' }}</span>
                  </div>
                  <div v-if="material.alternativeBrand" class="detail-tag alternative">
                    <font-awesome-icon icon="exchange-alt" />
                    <span>平替：{{ material.alternativeBrand }} {{ material.alternativeModel }}</span>
                  </div>
                </div>
              </div>

              <div class="inquiry-info">
                <div class="inquiry-meta">
                  <span class="inquiry-number">询价单：#{{ material.inquiryNumber }}</span>
                  <span class="inquiry-time">询价时间：{{ formatDate(material.inquiryTime) }}</span>
                  <span class="inquiry-deadline">截止时间：{{ formatDate(material.deadline) }}</span>
                </div>
              </div>
            </div>

            <div class="material-actions">
              <div class="action-buttons">
                <button 
                  class="action-btn primary"
                  :disabled="material.status === 'cancelled' || material.status === 'expired'"
                  @click="convertMaterialToPurchase(material)"
                >
                  <font-awesome-icon icon="shopping-cart" />
                  转采购单
                </button>
              </div>
              <div class="action-buttons secondary">
                <button class="action-btn edit" @click="editMaterial(material)">
                  <font-awesome-icon icon="edit" />
                </button>
                <button class="action-btn delete" @click="deleteMaterial(material)">
                  <font-awesome-icon icon="trash" />
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 确认弹窗 -->
    <nut-popup v-model:visible="confirmDialogVisible" position="center" round>
      <div class="confirm-dialog">
        <div class="confirm-header">
          <font-awesome-icon :icon="confirmType === 'delete' ? 'exclamation-triangle' : 'question-circle'" />
          <h3>{{ confirmTitle }}</h3>
        </div>
        <div class="confirm-content">
          <p>{{ confirmMessage }}</p>
        </div>
        <div class="confirm-actions">
          <button class="confirm-btn cancel" @click="confirmDialogVisible = false">
            取消
          </button>
          <button
            class="confirm-btn"
            :class="confirmType"
            @click="handleConfirm"
          >
            确认
          </button>
        </div>
      </div>
    </nut-popup>

    <!-- 供应商报价抽屉 -->
    <SupplierQuotationDrawer
      v-model:visible="quotationDrawerVisible"
      :material-data="selectedMaterial"
      @adopt-quotation="handleAdoptQuotation"
      @contact-supplier="handleContactSupplier"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import SupplierQuotationDrawer from '../components/SupplierQuotationDrawer.vue'

const router = useRouter()

// 响应式数据
const currentView = ref('order') // 'order' | 'material'
const selectedStatus = ref(null)
const confirmDialogVisible = ref(false)
const confirmType = ref('')
const confirmTitle = ref('')
const confirmMessage = ref('')
const confirmAction = ref(null)

// 供应商报价抽屉相关
const quotationDrawerVisible = ref(false)
const selectedMaterial = ref({})

// 搜索相关
const searchKeyword = ref('')
const showAdvancedFilters = ref(false)
const filters = ref({
  model: '',
  brand: '',
  acceptAlternative: '',
  inquiryNumber: '',
  inquiryTimeStart: '',
  inquiryTimeEnd: '',
  deadlineStart: '',
  deadlineEnd: ''
})

// 物料状态选项
const materialStatusOptions = ref([
  { value: 'inquiring', label: '询价中', color: '#1890ff' },
  { value: 'adopted', label: '已采纳', color: '#52c41a' },
  { value: 'cancelled', label: '已取消', color: '#d9d9d9' },
  { value: 'expired', label: '已截止', color: '#ff4d4f' }
])

// 模拟数据
const orders = ref([
  {
    id: 1,
    number: 'INQ202401001',
    createdTime: new Date('2024-01-15T10:30:00'),
    deadline: new Date('2024-01-22T18:00:00'),
    materialTypeCount: 3,
    totalQuantity: 8,
    totalAmount: 15600,
    materials: [1, 2, 3]
  },
  {
    id: 2,
    number: 'INQ202401002',
    createdTime: new Date('2024-01-18T14:20:00'),
    deadline: new Date('2024-01-25T18:00:00'),
    materialTypeCount: 2,
    totalQuantity: 7,
    totalAmount: 12800,
    materials: [4, 5]
  },
  {
    id: 3,
    number: 'INQ202401003',
    createdTime: new Date('2024-01-20T09:45:00'),
    deadline: new Date('2024-01-27T18:00:00'),
    materialTypeCount: 4,
    totalQuantity: 15,
    totalAmount: 28900,
    materials: [6, 7, 8, 9]
  },
  {
    id: 4,
    number: 'INQ202401004',
    createdTime: new Date('2024-01-12T16:00:00'),
    deadline: new Date('2024-01-19T18:00:00'),
    materialTypeCount: 1,
    totalQuantity: 2,
    totalAmount: 3200,
    materials: [10]
  }
])

const materials = ref([
  {
    id: 1,
    name: '西门子PLC控制器',
    model: '6ES7 214-1BD23-0XB0',
    brand: '西门子',
    category: '控制系统',
    quantity: 2,
    unitPrice: 2800,
    expectedDate: new Date('2024-04-15'),
    acceptAlternative: false,
    alternativeBrand: null,
    alternativeModel: null,
    inquiryNumber: 'INQ202401001',
    inquiryTime: new Date('2024-01-15T10:30:00'),
    deadline: new Date('2024-01-22T18:00:00'),
    status: 'inquiring'
  },
  {
    id: 2,
    name: '施耐德接触器',
    model: 'LC1D09M7',
    brand: '施耐德',
    category: '电气元件',
    quantity: 5,
    unitPrice: 180,
    expectedDate: new Date('2024-04-20'),
    acceptAlternative: true,
    alternativeBrand: '正泰',
    alternativeModel: 'NC1-0910',
    inquiryNumber: 'INQ202401001',
    inquiryTime: new Date('2024-01-15T10:30:00'),
    deadline: new Date('2024-01-22T18:00:00'),
    status: 'inquiring'
  },
  {
    id: 3,
    name: 'ABB变频器',
    model: 'ACS550-01-03A3-4',
    brand: 'ABB',
    category: '传动设备',
    quantity: 1,
    unitPrice: 4800,
    expectedDate: new Date('2024-04-25'),
    acceptAlternative: false,
    alternativeBrand: null,
    alternativeModel: null,
    inquiryNumber: 'INQ202401001',
    inquiryTime: new Date('2024-01-15T10:30:00'),
    deadline: new Date('2024-01-22T18:00:00'),
    status: 'inquiring'
  },
  {
    id: 4,
    name: '欧姆龙光电传感器',
    model: 'E2E-X5ME1',
    brand: '欧姆龙',
    category: '传感器',
    quantity: 10,
    unitPrice: 120,
    expectedDate: new Date('2024-04-18'),
    acceptAlternative: true,
    alternativeBrand: '基恩士',
    alternativeModel: 'PZ-M11',
    inquiryNumber: 'INQ202401002',
    inquiryTime: new Date('2024-01-18T14:20:00'),
    deadline: new Date('2024-01-25T18:00:00'),
    status: 'adopted'
  },
  {
    id: 5,
    name: '三菱电机伺服电机',
    model: 'HG-KN23J-S100',
    brand: '三菱电机',
    category: '伺服系统',
    quantity: 2,
    unitPrice: 5600,
    expectedDate: new Date('2024-05-01'),
    acceptAlternative: false,
    alternativeBrand: null,
    alternativeModel: null,
    inquiryNumber: 'INQ202401002',
    inquiryTime: new Date('2024-01-18T14:20:00'),
    deadline: new Date('2024-01-25T18:00:00'),
    status: 'adopted'
  },
  {
    id: 6,
    name: '台达触摸屏',
    model: 'DOP-B07S515',
    brand: '台达',
    category: '人机界面',
    quantity: 1,
    unitPrice: 1800,
    expectedDate: new Date('2024-04-22'),
    acceptAlternative: true,
    alternativeBrand: '威纶通',
    alternativeModel: 'MT506TV',
    inquiryNumber: 'INQ202401003',
    inquiryTime: new Date('2024-01-20T09:45:00'),
    deadline: new Date('2024-01-27T18:00:00'),
    status: 'inquiring'
  },
  {
    id: 7,
    name: '倍福PLC模块',
    model: 'CX5120-0120',
    brand: '倍福',
    category: '控制系统',
    quantity: 3,
    unitPrice: 3200,
    expectedDate: new Date('2024-04-28'),
    acceptAlternative: false,
    alternativeBrand: null,
    alternativeModel: null,
    inquiryNumber: 'INQ202401003',
    inquiryTime: new Date('2024-01-20T09:45:00'),
    deadline: new Date('2024-01-27T18:00:00'),
    status: 'inquiring'
  },
  {
    id: 8,
    name: '安川变频器',
    model: 'CIMR-G7A4022',
    brand: '安川',
    category: '传动设备',
    quantity: 2,
    unitPrice: 4500,
    expectedDate: new Date('2024-04-30'),
    acceptAlternative: true,
    alternativeBrand: '富士',
    alternativeModel: 'FRN22G11S-4CX',
    inquiryNumber: 'INQ202401003',
    inquiryTime: new Date('2024-01-20T09:45:00'),
    deadline: new Date('2024-01-27T18:00:00'),
    status: 'inquiring'
  },
  {
    id: 9,
    name: '基恩士激光传感器',
    model: 'LR-W500',
    brand: '基恩士',
    category: '传感器',
    quantity: 4,
    unitPrice: 2800,
    expectedDate: new Date('2024-05-05'),
    acceptAlternative: false,
    alternativeBrand: null,
    alternativeModel: null,
    inquiryNumber: 'INQ202401003',
    inquiryTime: new Date('2024-01-20T09:45:00'),
    deadline: new Date('2024-01-27T18:00:00'),
    status: 'inquiring'
  },
  {
    id: 10,
    name: '菲尼克斯继电器',
    model: 'REL-MR-24DC/21',
    brand: '菲尼克斯',
    category: '电气元件',
    quantity: 2,
    unitPrice: 1600,
    expectedDate: new Date('2024-04-10'),
    acceptAlternative: true,
    alternativeBrand: '魏德米勒',
    alternativeModel: 'TRZ 24VDC 1CO',
    inquiryNumber: 'INQ202401004',
    inquiryTime: new Date('2024-01-12T16:00:00'),
    deadline: new Date('2024-01-19T18:00:00'),
    status: 'expired'
  }
])

// 计算属性
const filteredOrders = computed(() => {
  let result = orders.value

  // 关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    result = result.filter(order => 
      order.number.toLowerCase().includes(keyword)
    )
  }

  // 高级筛选
  if (filters.value.inquiryNumber) {
    result = result.filter(order => 
      order.number.toLowerCase().includes(filters.value.inquiryNumber.toLowerCase())
    )
  }

  if (filters.value.inquiryTimeStart) {
    result = result.filter(order => 
      new Date(order.createdTime) >= new Date(filters.value.inquiryTimeStart)
    )
  }

  if (filters.value.inquiryTimeEnd) {
    result = result.filter(order => 
      new Date(order.createdTime) <= new Date(filters.value.inquiryTimeEnd + 'T23:59:59')
    )
  }

  if (filters.value.deadlineStart) {
    result = result.filter(order => 
      new Date(order.deadline) >= new Date(filters.value.deadlineStart)
    )
  }

  if (filters.value.deadlineEnd) {
    result = result.filter(order => 
      new Date(order.deadline) <= new Date(filters.value.deadlineEnd + 'T23:59:59')
    )
  }

  return result
})

const filteredMaterials = computed(() => {
  let result = materials.value

  // 状态筛选
  if (selectedStatus.value && selectedStatus.value !== 'all') {
    result = result.filter(material => material.status === selectedStatus.value)
  }

  // 关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    result = result.filter(material => 
      material.model.toLowerCase().includes(keyword) ||
      material.brand.toLowerCase().includes(keyword) ||
      material.inquiryNumber.toLowerCase().includes(keyword) ||
      material.name.toLowerCase().includes(keyword)
    )
  }

  // 高级筛选
  if (filters.value.model) {
    result = result.filter(material => 
      material.model.toLowerCase().includes(filters.value.model.toLowerCase())
    )
  }

  if (filters.value.brand) {
    result = result.filter(material => 
      material.brand.toLowerCase().includes(filters.value.brand.toLowerCase())
    )
  }

  if (filters.value.acceptAlternative !== '') {
    const acceptAlternative = filters.value.acceptAlternative === 'true'
    result = result.filter(material => material.acceptAlternative === acceptAlternative)
  }

  if (filters.value.inquiryNumber) {
    result = result.filter(material => 
      material.inquiryNumber.toLowerCase().includes(filters.value.inquiryNumber.toLowerCase())
    )
  }

  if (filters.value.inquiryTimeStart) {
    result = result.filter(material => 
      new Date(material.inquiryTime) >= new Date(filters.value.inquiryTimeStart)
    )
  }

  if (filters.value.inquiryTimeEnd) {
    result = result.filter(material => 
      new Date(material.inquiryTime) <= new Date(filters.value.inquiryTimeEnd + 'T23:59:59')
    )
  }

  if (filters.value.deadlineStart) {
    result = result.filter(material => 
      new Date(material.deadline) >= new Date(filters.value.deadlineStart)
    )
  }

  if (filters.value.deadlineEnd) {
    result = result.filter(material => 
      new Date(material.deadline) <= new Date(filters.value.deadlineEnd + 'T23:59:59')
    )
  }

  return result
})

// 方法
const goBack = () => {
  router.back()
}

const goToInquiry = () => {
  router.push('/inquiry')
}

// 搜索相关方法
const handleSearch = () => {
  // 搜索是实时的，通过computed属性自动更新
}

const clearSearch = () => {
  searchKeyword.value = ''
}

const resetFilters = () => {
  filters.value = {
    model: '',
    brand: '',
    acceptAlternative: '',
    inquiryNumber: '',
    inquiryTimeStart: '',
    inquiryTimeEnd: '',
    deadlineStart: '',
    deadlineEnd: ''
  }
}

const applyFilters = () => {
  // 筛选是实时的，通过computed属性自动更新
  showAdvancedFilters.value = false
}

const getStatusColor = (status) => {
  const statusMap = {
    'inquiring': '#1890ff',
    'adopted': '#52c41a',
    'cancelled': '#d9d9d9',
    'expired': '#ff4d4f'
  }
  return statusMap[status] || '#d9d9d9'
}

const getStatusText = (status) => {
  const statusMap = {
    'inquiring': '询价中',
    'adopted': '已采纳',
    'cancelled': '已取消',
    'expired': '已截止'
  }
  return statusMap[status] || '未知'
}

const formatDate = (date, format = 'YYYY-MM-DD HH:mm') => {
  if (!date) return ''
  const d = new Date(date)
  
  if (format === 'YYYY-MM-DD') {
    return d.getFullYear() + '-' + 
           String(d.getMonth() + 1).padStart(2, '0') + '-' + 
           String(d.getDate()).padStart(2, '0')
  }
  
  return d.getFullYear() + '-' + 
         String(d.getMonth() + 1).padStart(2, '0') + '-' + 
         String(d.getDate()).padStart(2, '0') + ' ' +
         String(d.getHours()).padStart(2, '0') + ':' + 
         String(d.getMinutes()).padStart(2, '0')
}

// 单据操作
const convertToPurchase = (order) => {
  showConfirm('convert', '转换为采购单', `确定要将询价单 #${order.number} 转换为采购单吗？`, () => {
    console.log('转换采购单:', order)
    // 这里实现转换逻辑
    alert(`询价单 #${order.number} 已转换为采购单`)
  })
}

const shareOrder = (order) => {
  console.log('分享询价单:', order)
  // 跳转到分享页面
  router.push(`/inquiry-share/${order.number}`)
}

const inviteQuote = (order) => {
  console.log('邀请报价:', order)
  // 这里可以实现邀请供应商报价的功能
  alert(`已发送邀请，询价单：#${order.number}`)
}

const cancelOrder = (order) => {
  if (order.status === 'cancelled') return
  
  showConfirm('cancel', '取消询价单', `确定要取消询价单 #${order.number} 吗？取消后无法恢复。`, () => {
    order.status = 'cancelled'
    // 同时更新相关物料状态
    materials.value.forEach(material => {
      if (material.inquiryNumber === order.number) {
        material.status = 'cancelled'
      }
    })
    console.log('取消询价单:', order)
  })
}

const editOrder = (order) => {
  console.log('编辑询价单:', order)
  // 这里可以跳转到编辑页面或打开编辑弹窗
  router.push(`/inquiry?edit=${order.id}`)
}

const deleteOrder = (order) => {
  showConfirm('delete', '删除询价单', `确定要删除询价单 #${order.number} 吗？删除后无法恢复。`, () => {
    const index = orders.value.findIndex(o => o.id === order.id)
    if (index > -1) {
      orders.value.splice(index, 1)
    }
    // 同时删除相关物料
    materials.value = materials.value.filter(material => material.inquiryNumber !== order.number)
    console.log('删除询价单:', order)
  })
}

// 物料操作
const convertMaterialToPurchase = (material) => {
  showConfirm('convert', '转换为采购单', `确定要将物料"${material.name}"转换为采购单吗？`, () => {
    console.log('转换采购单:', material)
    alert(`物料"${material.name}"已转换为采购单`)
  })
}

const cancelMaterial = (material) => {
  if (material.status === 'cancelled') return
  
  showConfirm('cancel', '取消物料询价', `确定要取消物料"${material.name}"的询价吗？`, () => {
    material.status = 'cancelled'
    console.log('取消物料询价:', material)
  })
}

const editMaterial = (material) => {
  console.log('编辑物料:', material)
  // 这里可以跳转到编辑页面或打开编辑弹窗
  router.push(`/inquiry?edit=${material.inquiryNumber}&material=${material.id}`)
}

const deleteMaterial = (material) => {
  showConfirm('delete', '删除物料', `确定要删除物料"${material.name}"吗？删除后无法恢复。`, () => {
    const index = materials.value.findIndex(m => m.id === material.id)
    if (index > -1) {
      materials.value.splice(index, 1)
    }
    console.log('删除物料:', material)
  })
}

// 供应商报价抽屉相关方法
const showSupplierQuotations = (material) => {
  selectedMaterial.value = material
  quotationDrawerVisible.value = true
}

const handleAdoptQuotation = (quotation) => {
  console.log('采纳报价:', quotation)
  // 这里可以实现采纳报价的逻辑
  alert(`已采纳 ${quotation.supplierName} 的报价：¥${quotation.totalPrice.toLocaleString()}`)
}

const handleContactSupplier = (quotation) => {
  console.log('联系供应商:', quotation)
  // 这里可以实现联系供应商的逻辑
  alert(`正在联系供应商：${quotation.supplierName}`)
}

// 确认弹窗
const showConfirm = (type, title, message, action) => {
  confirmType.value = type
  confirmTitle.value = title
  confirmMessage.value = message
  confirmAction.value = action
  confirmDialogVisible.value = true
}

const handleConfirm = () => {
  if (confirmAction.value) {
    confirmAction.value()
  }
  confirmDialogVisible.value = false
}

onMounted(() => {
  // 页面初始化
  console.log('询价管理页面已加载')
})
</script>

<style scoped>
.inquiry-management-container {
  min-height: 100vh;
  background-color: #f8f9fa;
  display: flex;
  flex-direction: column;
}

/* 导航栏样式 */
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background-color: #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-left,
.header-right {
  width: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.header-center {
  flex: 1;
  text-align: center;
}

.page-title {
  font-size: 18px;
  font-weight: 600;
  color: #2a2a35;
  margin: 0;
}

.back-icon,
.add-icon {
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  transition: background-color 0.2s ease;
}

.back-icon:hover,
.add-icon:hover {
  background-color: #f0f0f0;
}

.main-content {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

/* 搜索区域 */
.search-section {
  background: white;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.search-bar-row {
  display: flex;
  gap: 12px;
  align-items: center;
}

.search-bar {
  position: relative;
  flex: 1;
  display: flex;
  align-items: center;
}

.search-icon {
  position: absolute;
  left: 12px;
  color: #999;
  z-index: 1;
}

.search-input {
  width: 100%;
  padding: 12px 40px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  font-size: 14px;
  outline: none;
  transition: border-color 0.2s ease;
}

.search-input:focus {
  border-color: #f94c30;
}

.clear-btn {
  position: absolute;
  right: 8px;
  width: 24px;
  height: 24px;
  border: none;
  background: #f0f0f0;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 12px;
}

.clear-btn:hover {
  background: #e0e0e0;
}

.advanced-filter-toggle {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 12px 16px;
  background: #f8f9fa;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  font-size: 14px;
  color: #666;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
  height: fit-content;
}

.advanced-filter-toggle:hover {
  background: #f0f0f0;
  border-color: #f94c30;
  color: #f94c30;
}

.toggle-icon {
  margin-left: 4px;
}

.advanced-filters {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
  border-top: 1px solid #f0f0f0;
  margin-top: 12px;
}

.advanced-filters.expanded {
  max-height: 500px;
  padding-top: 16px;
}

.filter-row {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.filter-group {
  flex: 1;
  min-width: 200px;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.filter-group label {
  font-size: 12px;
  color: #666;
  font-weight: 500;
}

.filter-group input,
.filter-group select {
  padding: 8px 12px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  font-size: 13px;
  outline: none;
}

.filter-group input:focus,
.filter-group select:focus {
  border-color: #f94c30;
}

.date-separator {
  align-self: flex-end;
  padding: 8px 4px;
  font-size: 12px;
  color: #999;
}

.filter-actions {
  display: flex;
  gap: 8px;
  align-self: flex-end;
}

.filter-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 8px 12px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  background: white;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s ease;
}

.filter-btn.reset {
  color: #666;
}

.filter-btn.reset:hover {
  background: #f5f5f5;
}

.filter-btn.apply {
  background: #f94c30;
  border-color: #f94c30;
  color: white;
}

.filter-btn.apply:hover {
  background: #e0431b;
}

/* 控制区域 */
.control-section {
  background: white;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.view-tabs {
  display: flex;
  gap: 8px;
}

.view-tab {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 16px;
  background: #f8f9fa;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
  font-weight: 500;
  color: #666;
}

.view-tab.active {
  background: linear-gradient(135deg, #f94c30, #e0431b);
  border-color: #f94c30;
  color: white;
  box-shadow: 0 2px 8px rgba(249, 76, 48, 0.3);
}

.view-tab:not(.active):hover {
  border-color: #f94c30;
  background: rgba(249, 76, 48, 0.05);
  color: #f94c30;
}

.filter-section {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.status-filter {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.status-chip {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  background: #f8f9fa;
  border: 1px solid #f0f0f0;
  border-radius: 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 12px;
  color: #666;
}

.status-chip.active {
  border-color: #f94c30;
  background: rgba(249, 76, 48, 0.1);
  color: #f94c30;
}

.status-chip:hover {
  border-color: #f94c30;
  background: rgba(249, 76, 48, 0.05);
}

.status-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
}

/* 空状态 */
.empty-state {
  background: white;
  border-radius: 12px;
  padding: 40px 20px;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.empty-icon {
  margin-bottom: 16px;
  color: #ddd;
}

.empty-state h3 {
  font-size: 16px;
  color: #2a2a35;
  margin: 0 0 8px 0;
}

.empty-state p {
  font-size: 14px;
  color: #999;
  margin: 0 0 20px 0;
}

.create-btn {
  background: linear-gradient(135deg, #f94c30, #e0431b);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 10px 20px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 6px;
}

.create-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(249, 76, 48, 0.3);
}

/* 单据列表 */
.orders-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.order-item {
  background: white;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  transition: all 0.2s ease;
}

.order-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.order-info {
  flex: 1;
}

.order-number {
  font-size: 16px;
  font-weight: 600;
  color: #2a2a35;
  margin: 0 0 6px 0;
}

.order-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #666;
}

.order-divider {
  color: #ddd;
}

.order-status {
  display: flex;
  align-items: center;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  color: white;
}

.status-badge.small {
  padding: 2px 6px;
  font-size: 11px;
}

.order-details {
  border-top: 1px solid #f5f5f5;
  padding-top: 12px;
  margin-bottom: 12px;
}

.detail-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.detail-tag {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  background: #f8f9fa;
  border-radius: 16px;
  font-size: 12px;
  color: #666;
}

.detail-tag.alternative {
  background: rgba(39, 174, 96, 0.1);
  color: #27ae60;
}

.detail-tag.total-price {
  background: rgba(249, 76, 48, 0.1);
  color: #f94c30;
  font-weight: 500;
}

.detail-tag svg {
  width: 12px;
  height: 12px;
}

.order-actions,
.material-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 12px;
  border-top: 1px solid #f5f5f5;
  padding-top: 12px;
  flex-wrap: wrap;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.action-buttons.secondary {
  display: flex;
  gap: 8px;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 12px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  background: white;
  color: #666;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-btn:hover {
  transform: translateY(-1px);
}

.action-btn.primary {
  background: linear-gradient(135deg, #f94c30, #e0431b);
  border-color: #f94c30;
  color: white;
}

.action-btn.primary:hover {
  box-shadow: 0 2px 8px rgba(249, 76, 48, 0.3);
}

.action-btn.primary:disabled {
  background: #d9d9d9;
  border-color: #d9d9d9;
  color: #999;
  cursor: not-allowed;
  transform: none;
}

.action-btn.danger {
  border-color: #ff4757;
  color: #ff4757;
}

.action-btn.danger:hover {
  background: rgba(255, 71, 87, 0.1);
  box-shadow: 0 2px 8px rgba(255, 71, 87, 0.2);
}

/* 统一编辑和删除按钮样式，与Inquiry页面保持一致 */
.action-btn.edit {
  width: 32px;
  height: 32px;
  padding: 0;
  justify-content: center;
  background: rgba(249, 76, 48, 0.1);
  color: #f94c30;
  border: none;
}

.action-btn.edit:hover {
  background: rgba(249, 76, 48, 0.2);
  transform: none;
  box-shadow: none;
}

.action-btn.delete {
  width: 32px;
  height: 32px;
  padding: 0;
  justify-content: center;
  background: rgba(255, 71, 87, 0.1);
  color: #ff4757;
  border: none;
}

.action-btn.delete:hover {
  background: rgba(255, 71, 87, 0.2);
  transform: none;
  box-shadow: none;
}

/* 物料列表 */
.materials-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.material-item {
  background: white;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  transition: all 0.2s ease;
  cursor: pointer;
}

.material-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
}

.material-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.material-info {
  flex: 1;
}

.material-name {
  font-size: 16px;
  font-weight: 600;
  color: #2a2a35;
  margin: 0 0 6px 0;
  line-height: 1.3;
}

.material-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;
  color: #666;
}

.material-model {
  font-weight: 500;
}

.material-divider {
  color: #ddd;
}

.material-brand {
  color: #f94c30;
  font-weight: 500;
}

.material-category {
  color: #27ae60;
  font-weight: 500;
}

.material-status {
  display: flex;
  align-items: center;
}

.material-details {
  border-top: 1px solid #f5f5f5;
  padding-top: 12px;
  margin-bottom: 12px;
}

.detail-row {
  margin-bottom: 8px;
}

.detail-row:last-child {
  margin-bottom: 0;
}

.inquiry-info {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 8px 12px;
  margin-top: 8px;
}

.inquiry-meta {
  display: flex;
  flex-direction: column;
  gap: 4px;
  font-size: 11px;
  color: #666;
}

.inquiry-number {
  color: #f94c30;
  font-weight: 500;
}

.material-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  border-top: 1px solid #f5f5f5;
  padding-top: 12px;
}

/* 确认弹窗 */
.confirm-dialog {
  padding: 20px;
  text-align: center;
  max-width: 320px;
}

.confirm-header {
  margin-bottom: 16px;
}

.confirm-header svg {
  font-size: 32px;
  color: #f94c30;
  margin-bottom: 8px;
}

.confirm-header h3 {
  font-size: 16px;
  font-weight: 600;
  color: #2a2a35;
  margin: 0;
}

.confirm-content {
  margin-bottom: 20px;
}

.confirm-content p {
  font-size: 14px;
  color: #666;
  line-height: 1.4;
  margin: 0;
}

.confirm-actions {
  display: flex;
  gap: 12px;
}

.confirm-btn {
  flex: 1;
  padding: 10px 16px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  background: white;
  color: #666;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.confirm-btn.cancel {
  border-color: #d9d9d9;
  color: #999;
}

.confirm-btn.cancel:hover {
  background: #f5f5f5;
}

.confirm-btn.convert {
  background: linear-gradient(135deg, #f94c30, #e0431b);
  border-color: #f94c30;
  color: white;
}

.confirm-btn.convert:hover {
  box-shadow: 0 2px 8px rgba(249, 76, 48, 0.3);
}

.confirm-btn.cancel:not(.cancel) {
  background: #1890ff;
  border-color: #1890ff;
  color: white;
}

.confirm-btn.delete {
  background: #ff4757;
  border-color: #ff4757;
  color: white;
}

.confirm-btn.delete:hover {
  box-shadow: 0 2px 8px rgba(255, 71, 87, 0.3);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .filter-row {
    flex-direction: column;
    gap: 12px;
  }
  
  .filter-group {
    min-width: auto;
  }
  
  .filter-actions {
    align-self: stretch;
    justify-content: center;
  }
  
  .view-tabs {
    flex-direction: column;
    gap: 6px;
  }
  
  .order-actions,
  .material-actions {
    flex-direction: column;
    align-items: stretch;
  }
  
  .action-buttons {
    justify-content: center;
  }
  
  .action-buttons.secondary {
    justify-content: center;
  }
  
  .action-btn:not(.edit):not(.delete) {
    justify-content: center;
    padding: 8px 12px;
  }
  
  .detail-tags {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .material-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  
  .order-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  
  .confirm-actions {
    flex-direction: column;
  }
}
</style>