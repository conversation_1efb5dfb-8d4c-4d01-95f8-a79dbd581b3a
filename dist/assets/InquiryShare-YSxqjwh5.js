import{r as d,k as B,x as L,c as m,b as F,a as t,d as i,p as V,e as f,y as I,t as n,F as $,h as j,w as h,o as _,g as Q,s as E,f as c,n as O}from"./index-Dndsgo0C.js";import{S as H}from"./SupplierQuotationDrawer-BEoOSMGz.js";import{_ as P}from"./_plugin-vue_export-helper-DlAUqK2U.js";const z={class:"inquiry-share-container"},G={class:"main-content"},R={class:"inquiry-basic-info"},U={class:"info-content"},X={class:"company-info-item"},J={class:"company-details"},K={class:"company-name"},W={class:"inquiry-number"},Z={class:"info-grid"},tt={class:"info-item"},et={class:"value"},st={class:"info-item"},at={class:"value"},nt={class:"info-item"},ot={class:"value"},it={class:"info-item"},lt={class:"value"},dt={class:"info-item"},rt={class:"value"},ct={class:"info-item"},ut={class:"value highlight"},pt={class:"material-details"},vt={class:"section-header"},mt={class:"material-count"},_t={class:"materials-list"},gt=["onClick"],ft={class:"material-line-1"},ht={class:"material-name"},bt={class:"material-line-2"},yt={class:"material-model"},Dt={class:"material-quantity"},St={class:"material-line-3"},Ct={class:"material-line-4"},qt={class:"expected-date"},wt={key:0,class:"material-line-5"},Mt={class:"material-remark"},xt={class:"material-line-6"},kt={class:"quote-progress-text"},Tt={class:"success-count"},At={class:"rejected-count"},Yt={class:"view-quotes-btn"},Nt={class:"login-prompt"},Bt={class:"prompt-header"},Lt={__name:"InquiryShare",setup(Ft){const q=L(),b=d(!1),u=d(!1),p=d(!1),g=d(!1),y=d({}),l=d({id:1,number:"RFQ202401001",companyName:"上海电气自动化研究所",createdTime:new Date("2024-01-15T10:30:00"),deadline:new Date("2024-01-22T18:00:00"),inquirer:"张工程师",phone:"13812348888",materialTypeCount:3,adoptedTotalAmount:8600}),D=d([{id:1,name:"西门子PLC控制器",model:"6ES7 214-1BD23-0XB0",brand:"西门子",category:"控制系统",quantity:2,expectedDate:new Date("2026-04-15"),acceptAlternative:!1,status:"inquiring",supplierCount:5,quotedCount:3,rejectedCount:1,remark:"需要原装正品，有CE认证"},{id:2,name:"施耐德接触器",model:"LC1D09M7",brand:"施耐德",category:"电气元件",quantity:5,expectedDate:new Date("2024-04-20"),acceptAlternative:!0,status:"adopted",supplierCount:4,quotedCount:4,rejectedCount:0,remark:"可接受正泰等品牌平替"},{id:3,name:"ABB变频器",model:"ACS550-01-03A3-4",brand:"ABB",category:"传动设备",quantity:1,expectedDate:new Date("2024-04-25"),acceptAlternative:!1,status:"inquiring",supplierCount:3,quotedCount:2,rejectedCount:1,remark:"需要配套技术支持"}]),S=(a,e="YYYY-MM-DD HH:mm")=>{if(!a)return"";const o=new Date(a);return e==="YYYY-MM-DD"?o.getFullYear()+"-"+String(o.getMonth()+1).padStart(2,"0")+"-"+String(o.getDate()).padStart(2,"0"):e==="MM-DD"?String(o.getMonth()+1).padStart(2,"0")+"-"+String(o.getDate()).padStart(2,"0"):o.getFullYear()+"-"+String(o.getMonth()+1).padStart(2,"0")+"-"+String(o.getDate()).padStart(2,"0")+" "+String(o.getHours()).padStart(2,"0")+":"+String(o.getMinutes()).padStart(2,"0")},w=a=>{if(!a)return"";const e=new Date(a),o=new Date,v=new Date(o.getFullYear(),o.getMonth(),o.getDate()),s=new Date(e.getFullYear(),e.getMonth(),e.getDate()).getTime()-v.getTime(),r=Math.ceil(s/(1e3*60*60*24));return r<0?`已过期${Math.abs(r)}天`:r===0?"今日发货":r===1?"明日发货":`${r}日发货`},M=a=>({inquiring:"#1890ff",adopted:"#52c41a",cancelled:"#d9d9d9",expired:"#ff4d4f"})[a]||"#d9d9d9",x=a=>({inquiring:"询价中",adopted:"已采纳",cancelled:"已取消",expired:"已截止"})[a]||"未知",k=()=>{u.value=!1,b.value=!0},T=()=>{p.value=!p.value},A=a=>{if(!b.value){u.value=!0;return}y.value=a,g.value=!0},Y=a=>{console.log("采纳报价:",a),alert(`已采纳 ${a.supplierName} 的报价：¥${a.totalPrice.toLocaleString()}`)},N=a=>{console.log("联系供应商:",a),alert(`正在联系供应商：${a.supplierName}`)};return B(()=>{const a=q.params.inquiryNumber;console.log("加载询价单数据:",a)}),(a,e)=>{const o=f("font-awesome-icon"),v=f("nut-tag"),C=f("nut-popup");return _(),m("div",z,[e[15]||(e[15]=F('<div class="header" data-v-7f943bce><div class="header-content" data-v-7f943bce><div class="logo-section" data-v-7f943bce><div class="company-logo-text" data-v-7f943bce>研选LOGO</div></div><div class="share-title" data-v-7f943bce><h2 data-v-7f943bce>询价单</h2></div><span data-v-7f943bce></span></div></div>',1)),t("div",G,[t("div",R,[t("div",{class:"section-header",onClick:T},[i(o,{icon:"file-invoice",class:"section-icon"}),e[2]||(e[2]=t("h4",null,"基本信息",-1)),i(o,{icon:p.value?"chevron-down":"chevron-up",class:"collapse-icon"},null,8,["icon"])]),V(t("div",U,[t("div",X,[e[3]||(e[3]=t("div",{class:"company-logo"},[t("div",{class:"logo-placeholder"},"LOGO")],-1)),t("div",J,[t("div",K,n(l.value.companyName),1),t("div",W,"询价单号："+n(l.value.number),1)])]),t("div",Z,[t("div",tt,[e[4]||(e[4]=t("label",null,"询价时间",-1)),t("span",et,n(S(l.value.createdTime)),1)]),t("div",st,[e[5]||(e[5]=t("label",null,"截止时间",-1)),t("span",at,n(S(l.value.deadline)),1)]),t("div",nt,[e[6]||(e[6]=t("label",null,"询价人",-1)),t("span",ot,n(l.value.inquirer),1)]),t("div",it,[e[7]||(e[7]=t("label",null,"联系电话",-1)),t("span",lt,n(l.value.phone),1)]),t("div",dt,[e[8]||(e[8]=t("label",null,"物料型号数",-1)),t("span",rt,n(l.value.materialTypeCount)+" 种",1)]),t("div",ct,[e[9]||(e[9]=t("label",null,"已采纳总价",-1)),t("span",ut,"¥"+n(l.value.adoptedTotalAmount.toLocaleString()),1)])])],512),[[I,!p.value]])]),t("div",pt,[t("div",vt,[i(o,{icon:"boxes",class:"section-icon"}),e[10]||(e[10]=t("h4",null,"物料明细",-1)),t("span",mt,"共 "+n(D.value.length)+" 项物料",1)]),t("div",_t,[(_(!0),m($,null,j(D.value,s=>(_(),m("div",{key:s.id,class:"material-item",onClick:r=>A(s)},[t("div",ft,[t("h4",ht,n(s.name),1),t("span",{class:"status-badge",style:E({backgroundColor:M(s.status)})},n(x(s.status)),5)]),t("div",bt,[t("span",yt,n(s.model),1),t("span",Dt,"x"+n(s.quantity),1)]),t("div",St,[i(v,{class:"material-brand",type:"danger"},{default:h(()=>[c(n(s.brand),1)]),_:2},1024),i(v,{class:"material-category",type:"success"},{default:h(()=>[c(n(s.category),1)]),_:2},1024)]),t("div",Ct,[t("span",qt,"期望交期："+n(w(s.expectedDate)),1),t("span",{class:O(["accept-alternative",{yes:s.acceptAlternative}])},n(s.acceptAlternative?"接受平替":"不接受平替"),3)]),s.remark?(_(),m("div",wt,[t("div",Mt,n(s.remark),1)])):Q("",!0),t("div",xt,[t("span",kt,[c(" 供应商数："+n(s.supplierCount)+" 已报价：",1),t("span",Tt,n(s.quotedCount),1),e[11]||(e[11]=c(" 已拒绝：")),t("span",At,n(s.rejectedCount),1)]),t("span",Yt,[e[12]||(e[12]=c(" 查看 ")),i(o,{icon:"chevron-right"})])])],8,gt))),128))])])]),i(C,{visible:u.value,"onUpdate:visible":e[0]||(e[0]=s=>u.value=s),position:"center",round:""},{default:h(()=>[t("div",Nt,[t("div",Bt,[i(o,{icon:"lock"}),e[13]||(e[13]=t("h3",null,"需要登录",-1))]),e[14]||(e[14]=t("div",{class:"prompt-content"},[t("p",null,"登录后即可查看供应商报价详情")],-1)),t("div",{class:"prompt-actions"},[t("button",{class:"prompt-btn login",onClick:k}," 手机号一键登录 ")])])]),_:1},8,["visible"]),i(H,{visible:g.value,"onUpdate:visible":e[1]||(e[1]=s=>g.value=s),"material-data":y.value,onAdoptQuotation:Y,onContactSupplier:N},null,8,["visible","material-data"])])}}},jt=P(Lt,[["__scopeId","data-v-7f943bce"]]);export{jt as default};
