import{r as d,l as R,k as Y,c as f,a as t,d as a,e as c,t as _,w as i,u as Q,o as m,i as x,g as U,f as z,F as q,h as J,n as X}from"./index-Dndsgo0C.js";import{_ as ee}from"./_plugin-vue_export-helper-DlAUqK2U.js";const te={class:"page-container"},oe={class:"header"},le={class:"header-left"},ae={class:"main-content"},se={class:"profile-header"},ie={class:"avatar-section"},ne={class:"avatar-container"},re=["src"],de={class:"user-basic-info"},ce={class:"username"},ue={class:"workspace-name"},me={class:"form-container"},pe={class:"section"},ve={class:"section-content"},fe={class:"section"},_e={class:"section-content"},be={class:"certificate-upload"},ge=["src"],ye={class:"submit-section"},ke={class:"workspace-modal"},we={class:"modal-body"},Ie={class:"workspace-list"},Ce=["onClick"],he={class:"workspace-info"},Ve={class:"workspace-icon"},Ne={class:"workspace-details"},De={class:"workspace-status"},xe="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMzIiIGN5PSIzMiIgcj0iMzIiIGZpbGw9IiNmMGYwZjAiLz4KPGNpcmNsZSBjeD0iMzIiIGN5PSIyNCIgcj0iMTAiIGZpbGw9IiNjY2MiLz4KPHBhdGggZD0iTTEyIDUyYzAtMTEuMDQ2IDguOTU0LTIwIDIwLTIwczIwIDguOTU0IDIwIDIwSDEyeiIgZmlsbD0iI2NjYyIvPgo8L3N2Zz4K",Ue={__name:"Profile",setup(ze){const M=Q(),C=d(null),l=R({userId:"U001",username:"张三",avatar:"",realName:"张三",birthday:new Date("1990-01-01"),bio:"这个人很懒，什么都没有留下...",identity:"employee",company:"ABC科技有限公司",position:"前端开发工程师",workCertificate:""}),h=[{text:"员工",value:"employee"},{text:"管理者",value:"manager"},{text:"主管",value:"director"},{text:"管理员",value:"admin"}],b=d(!1),g=d(!1),p=d(!1),v=d(!1),y=d({id:"ws1",name:"个人空间",description:"个人空间",icon:"building"}),A=d([{id:"ws1",name:"个人空间",description:"个人空间",icon:"building"},{id:"ws2",name:"项目A空间",description:"项目A专用工作空间",icon:"project-diagram"},{id:"ws3",name:"测试空间",description:"用于测试的工作空间",icon:"flask"}]),w=d(null),k=d(null),Z=()=>{M.back()},B=s=>s?typeof s=="string"?s:s.toISOString().split("T")[0]:"",P=s=>{const e=h.find(n=>n.value===s);return e?e.text:""},j=s=>{l.birthday=s[0],p.value=!1},G=s=>{l.identity=s[0].value,v.value=!1},T=(s,e,n)=>{console.log("验证结果:",s,e,n)},L=async()=>{try{b.value=!0,await C.value.validate()&&(console.log("提交表单数据:",l),await new Promise(e=>setTimeout(e,2e3)),console.log("保存成功！"))}catch(s){console.error("表单验证失败:",s),console.log("请检查表单信息")}finally{b.value=!1}},V=()=>{k.value="avatar",w.value.click()},N=()=>{k.value="workCertificate",w.value.click()},S=s=>{const e=s.target.files[0];if(!e)return;if(!e.type.startsWith("image/")){alert("请选择图片文件");return}if(e.size>5*1024*1024){alert("图片大小不能超过5MB");return}const n=new FileReader;n.onload=u=>{const r=u.target.result;k.value==="avatar"?l.avatar=r:k.value==="workCertificate"&&(l.workCertificate=r)},n.readAsDataURL(e),s.target.value=""},F=s=>{window.open(s,"_blank")},W=s=>{y.value=s,g.value=!1,console.log("切换到工作空间:",s)};Y(()=>{$()});const $=()=>{console.log("加载用户资料")};return(s,e)=>{const n=c("font-awesome-icon"),u=c("nut-input"),r=c("nut-form-item"),E=c("nut-date-picker"),I=c("nut-popup"),H=c("nut-textarea"),K=c("nut-picker"),D=c("nut-button"),O=c("nut-form");return m(),f("div",te,[t("div",oe,[t("div",le,[a(n,{icon:"arrow-left",size:"lg",color:"#2a2a35",onClick:Z,class:"back-icon"})]),e[16]||(e[16]=t("div",{class:"header-center"},[t("h2",{class:"page-title"},"个人中心")],-1))]),t("div",ae,[t("div",se,[t("div",ie,[t("div",ne,[t("img",{src:l.avatar||xe,alt:"用户头像",class:"avatar",onClick:V},null,8,re),t("div",{class:"avatar-edit-overlay",onClick:V},[a(n,{icon:"camera",color:"#ffffff"})])]),t("div",de,[t("h3",ce,_(l.username||"未设置用户名"),1),t("div",{class:"workspace-selector",onClick:e[0]||(e[0]=o=>g.value=!0)},[t("span",ue,_(y.value.name),1),a(n,{icon:"chevron-down"})])])])]),t("div",me,[a(O,{ref_key:"profileForm",ref:C,"model-value":l,onValidate:T},{default:i(()=>[t("div",pe,[e[17]||(e[17]=t("div",{class:"section-header"},[t("h4",{class:"section-title"},"基本信息")],-1)),t("div",ve,[a(r,{label:"用户名",prop:"username",rules:[{required:!0,message:"请输入用户名"},{min:2,max:20,message:"用户名长度为2-20个字符"}]},{default:i(()=>[a(u,{modelValue:l.username,"onUpdate:modelValue":e[1]||(e[1]=o=>l.username=o),placeholder:"请输入用户名",clearable:""},null,8,["modelValue"])]),_:1}),a(r,{label:"生日",prop:"birthday"},{default:i(()=>[a(u,{"model-value":B(l.birthday),placeholder:"请选择生日",readonly:"",onClick:e[2]||(e[2]=o=>p.value=!0)},null,8,["model-value"]),a(I,{visible:p.value,"onUpdate:visible":e[5]||(e[5]=o=>p.value=o),position:"bottom"},{default:i(()=>[a(E,{modelValue:l.birthday,"onUpdate:modelValue":e[3]||(e[3]=o=>l.birthday=o),type:"date",title:"选择生日","min-date":new Date("1950-01-01"),"max-date":new Date,onConfirm:j,onClose:e[4]||(e[4]=o=>p.value=!1)},null,8,["modelValue","min-date","max-date"])]),_:1},8,["visible"])]),_:1}),a(r,{label:"个人简介",prop:"bio"},{default:i(()=>[a(H,{modelValue:l.bio,"onUpdate:modelValue":e[6]||(e[6]=o=>l.bio=o),placeholder:"请输入个人简介",rows:3,"max-length":200,"show-word-limit":""},null,8,["modelValue"])]),_:1})])]),t("div",fe,[e[20]||(e[20]=t("div",{class:"section-header"},[t("h4",{class:"section-title"},"职业信息")],-1)),t("div",_e,[a(r,{label:"真实姓名",prop:"realName",rules:[{min:2,max:10,message:"姓名长度为2-10个字符"}]},{default:i(()=>[a(u,{modelValue:l.realName,"onUpdate:modelValue":e[7]||(e[7]=o=>l.realName=o),placeholder:"请输入真实姓名",clearable:""},null,8,["modelValue"])]),_:1}),a(r,{label:"身份",prop:"identity"},{default:i(()=>[a(u,{"model-value":P(l.identity),placeholder:"请选择身份",readonly:"",onClick:e[8]||(e[8]=o=>v.value=!0)},null,8,["model-value"]),a(I,{visible:v.value,"onUpdate:visible":e[11]||(e[11]=o=>v.value=o),position:"bottom"},{default:i(()=>[a(K,{modelValue:l.identity,"onUpdate:modelValue":e[9]||(e[9]=o=>l.identity=o),columns:h,title:"选择身份",onConfirm:G,onClose:e[10]||(e[10]=o=>v.value=!1)},null,8,["modelValue"])]),_:1},8,["visible"])]),_:1}),a(r,{label:"公司",prop:"company",rules:[{min:2,max:50,message:"公司名称长度为2-50个字符"}]},{default:i(()=>[a(u,{modelValue:l.company,"onUpdate:modelValue":e[12]||(e[12]=o=>l.company=o),placeholder:"请输入公司名称",clearable:""},null,8,["modelValue"])]),_:1}),a(r,{label:"职位",prop:"position",rules:[{min:2,max:30,message:"职位名称长度为2-30个字符"}]},{default:i(()=>[a(u,{modelValue:l.position,"onUpdate:modelValue":e[13]||(e[13]=o=>l.position=o),placeholder:"请输入职位",clearable:""},null,8,["modelValue"])]),_:1}),a(r,{label:"工作证照",prop:"workCertificate"},{default:i(()=>[t("div",be,[l.workCertificate?(m(),f("img",{key:0,src:l.workCertificate,alt:"工作证照",class:"cert-image",onClick:e[14]||(e[14]=o=>F(l.workCertificate))},null,8,ge)):(m(),f("div",{key:1,class:"upload-placeholder",onClick:N},[a(n,{icon:"plus"}),e[18]||(e[18]=t("span",null,"上传工作证照",-1))])),l.workCertificate?(m(),x(D,{key:2,onClick:N,type:"default",size:"small",class:"change-image-btn"},{default:i(()=>e[19]||(e[19]=[z(" 更换证照 ")])),_:1})):U("",!0)])]),_:1})])]),t("div",ye,[a(D,{type:"primary",block:"",size:"large",loading:b.value,onClick:L,class:"submit-btn"},{default:i(()=>[z(_(b.value?"保存中...":"保存信息"),1)]),_:1},8,["loading"])])]),_:1},8,["model-value"])])]),a(I,{visible:g.value,"onUpdate:visible":e[15]||(e[15]=o=>g.value=o),position:"bottom",style:{height:"60%"},round:"",closeable:"","close-icon-position":"top-right"},{default:i(()=>[t("div",ke,[e[21]||(e[21]=t("div",{class:"modal-header"},[t("h3",null,"选择工作空间")],-1)),t("div",we,[t("div",Ie,[(m(!0),f(q,null,J(A.value,o=>(m(),f("div",{key:o.id,class:X(["workspace-item",{active:o.id===y.value.id}]),onClick:Me=>W(o)},[t("div",he,[t("div",Ve,[a(n,{icon:o.icon},null,8,["icon"])]),t("div",Ne,[t("h4",null,_(o.name),1),t("p",null,_(o.description),1)])]),t("div",De,[o.id===y.value.id?(m(),x(n,{key:0,icon:"check-circle",color:"#f94c30"})):U("",!0)])],10,Ce))),128))])])])]),_:1},8,["visible"]),t("input",{ref_key:"fileInput",ref:w,type:"file",accept:"image/*",style:{display:"none"},onChange:S},null,544)])}}},Be=ee(Ue,[["__scopeId","data-v-ed329180"]]);export{Be as default};
