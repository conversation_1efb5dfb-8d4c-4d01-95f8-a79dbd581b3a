import{r as u,m as V,k as de,c as d,a as t,d as s,e as Y,g as C,p as v,v as m,n as g,q as re,f as y,F as $,h as L,w as ue,u as ce,o as r,s as U,t as o}from"./index-Dndsgo0C.js";import{S as ve}from"./SupplierQuotationDrawer-BEoOSMGz.js";import{_ as pe}from"./_plugin-vue_export-helper-DlAUqK2U.js";const me={class:"inquiry-management-container"},ye={class:"header"},be={class:"header-left"},fe={class:"header-right"},ge={class:"main-content"},_e={class:"search-section"},we={class:"search-bar-row"},qe={class:"search-bar"},De={class:"filter-row"},Te={class:"filter-group"},Ce={class:"filter-group"},he={class:"filter-group"},ke={class:"filter-row"},Se={class:"filter-group"},Ne={class:"filter-group"},Me={class:"filter-row"},xe={class:"filter-group"},Ae={class:"filter-actions"},$e={class:"control-section"},Le={class:"view-tabs"},Qe={key:0,class:"filter-section"},Be={class:"status-filter"},Ee=["onClick"],Ie={key:0,class:"order-view"},Pe={key:0,class:"empty-state"},Ve={class:"empty-icon"},Ye={key:1,class:"orders-list"},Ue={class:"order-header"},Fe={class:"order-info"},Oe={class:"order-number"},Re={class:"order-meta"},ze={class:"order-time"},He={class:"order-deadline"},Xe={class:"order-details"},Ge={class:"detail-tags"},Ke={class:"detail-tag"},Ze={class:"detail-tag"},Je={class:"detail-tag"},We={class:"order-actions"},je={class:"action-buttons"},et=["onClick"],tt=["onClick"],nt=["onClick"],it={class:"action-buttons secondary"},at=["onClick"],lt=["onClick"],st={key:1,class:"material-view"},ot={key:0,class:"empty-state"},dt={class:"empty-icon"},rt={key:1,class:"materials-list"},ut=["onClick"],ct={class:"material-header"},vt={class:"material-info"},pt={class:"material-name"},mt={class:"material-meta"},yt={class:"material-model"},bt={class:"material-brand"},ft={class:"material-category"},gt={class:"material-status"},_t={class:"material-details"},wt={class:"detail-row"},qt={class:"detail-tags"},Dt={class:"detail-tag"},Tt={class:"detail-tag"},Ct={class:"detail-tag"},ht={class:"detail-tag total-price"},kt={class:"detail-row"},St={class:"detail-tags"},Nt={key:0,class:"detail-tag alternative"},Mt={class:"inquiry-info"},xt={class:"inquiry-meta"},At={class:"inquiry-number"},$t={class:"inquiry-time"},Lt={class:"inquiry-deadline"},Qt={class:"material-actions"},Bt={class:"action-buttons"},Et=["disabled","onClick"],It={class:"action-buttons secondary"},Pt=["onClick"],Vt=["onClick"],Yt={class:"confirm-dialog"},Ut={class:"confirm-header"},Ft={class:"confirm-content"},Ot={class:"confirm-actions"},Rt={__name:"InquiryManagement",setup(zt){const _=ce(),b=u("order"),f=u(null),w=u(!1),k=u(""),Q=u(""),B=u(""),S=u(null),N=u(!1),E=u({}),p=u(""),q=u(!1),l=u({model:"",brand:"",acceptAlternative:"",inquiryNumber:"",inquiryTimeStart:"",inquiryTimeEnd:"",deadlineStart:"",deadlineEnd:""}),F=u([{value:"inquiring",label:"询价中",color:"#1890ff"},{value:"adopted",label:"已采纳",color:"#52c41a"},{value:"cancelled",label:"已取消",color:"#d9d9d9"},{value:"expired",label:"已截止",color:"#ff4d4f"}]),M=u([{id:1,number:"INQ202401001",createdTime:new Date("2024-01-15T10:30:00"),deadline:new Date("2024-01-22T18:00:00"),materialTypeCount:3,totalQuantity:8,totalAmount:15600,materials:[1,2,3]},{id:2,number:"INQ202401002",createdTime:new Date("2024-01-18T14:20:00"),deadline:new Date("2024-01-25T18:00:00"),materialTypeCount:2,totalQuantity:7,totalAmount:12800,materials:[4,5]},{id:3,number:"INQ202401003",createdTime:new Date("2024-01-20T09:45:00"),deadline:new Date("2024-01-27T18:00:00"),materialTypeCount:4,totalQuantity:15,totalAmount:28900,materials:[6,7,8,9]},{id:4,number:"INQ202401004",createdTime:new Date("2024-01-12T16:00:00"),deadline:new Date("2024-01-19T18:00:00"),materialTypeCount:1,totalQuantity:2,totalAmount:3200,materials:[10]}]),D=u([{id:1,name:"西门子PLC控制器",model:"6ES7 214-1BD23-0XB0",brand:"西门子",category:"控制系统",quantity:2,unitPrice:2800,expectedDate:new Date("2024-04-15"),acceptAlternative:!1,alternativeBrand:null,alternativeModel:null,inquiryNumber:"INQ202401001",inquiryTime:new Date("2024-01-15T10:30:00"),deadline:new Date("2024-01-22T18:00:00"),status:"inquiring"},{id:2,name:"施耐德接触器",model:"LC1D09M7",brand:"施耐德",category:"电气元件",quantity:5,unitPrice:180,expectedDate:new Date("2024-04-20"),acceptAlternative:!0,alternativeBrand:"正泰",alternativeModel:"NC1-0910",inquiryNumber:"INQ202401001",inquiryTime:new Date("2024-01-15T10:30:00"),deadline:new Date("2024-01-22T18:00:00"),status:"inquiring"},{id:3,name:"ABB变频器",model:"ACS550-01-03A3-4",brand:"ABB",category:"传动设备",quantity:1,unitPrice:4800,expectedDate:new Date("2024-04-25"),acceptAlternative:!1,alternativeBrand:null,alternativeModel:null,inquiryNumber:"INQ202401001",inquiryTime:new Date("2024-01-15T10:30:00"),deadline:new Date("2024-01-22T18:00:00"),status:"inquiring"},{id:4,name:"欧姆龙光电传感器",model:"E2E-X5ME1",brand:"欧姆龙",category:"传感器",quantity:10,unitPrice:120,expectedDate:new Date("2024-04-18"),acceptAlternative:!0,alternativeBrand:"基恩士",alternativeModel:"PZ-M11",inquiryNumber:"INQ202401002",inquiryTime:new Date("2024-01-18T14:20:00"),deadline:new Date("2024-01-25T18:00:00"),status:"adopted"},{id:5,name:"三菱电机伺服电机",model:"HG-KN23J-S100",brand:"三菱电机",category:"伺服系统",quantity:2,unitPrice:5600,expectedDate:new Date("2024-05-01"),acceptAlternative:!1,alternativeBrand:null,alternativeModel:null,inquiryNumber:"INQ202401002",inquiryTime:new Date("2024-01-18T14:20:00"),deadline:new Date("2024-01-25T18:00:00"),status:"adopted"},{id:6,name:"台达触摸屏",model:"DOP-B07S515",brand:"台达",category:"人机界面",quantity:1,unitPrice:1800,expectedDate:new Date("2024-04-22"),acceptAlternative:!0,alternativeBrand:"威纶通",alternativeModel:"MT506TV",inquiryNumber:"INQ202401003",inquiryTime:new Date("2024-01-20T09:45:00"),deadline:new Date("2024-01-27T18:00:00"),status:"inquiring"},{id:7,name:"倍福PLC模块",model:"CX5120-0120",brand:"倍福",category:"控制系统",quantity:3,unitPrice:3200,expectedDate:new Date("2024-04-28"),acceptAlternative:!1,alternativeBrand:null,alternativeModel:null,inquiryNumber:"INQ202401003",inquiryTime:new Date("2024-01-20T09:45:00"),deadline:new Date("2024-01-27T18:00:00"),status:"inquiring"},{id:8,name:"安川变频器",model:"CIMR-G7A4022",brand:"安川",category:"传动设备",quantity:2,unitPrice:4500,expectedDate:new Date("2024-04-30"),acceptAlternative:!0,alternativeBrand:"富士",alternativeModel:"FRN22G11S-4CX",inquiryNumber:"INQ202401003",inquiryTime:new Date("2024-01-20T09:45:00"),deadline:new Date("2024-01-27T18:00:00"),status:"inquiring"},{id:9,name:"基恩士激光传感器",model:"LR-W500",brand:"基恩士",category:"传感器",quantity:4,unitPrice:2800,expectedDate:new Date("2024-05-05"),acceptAlternative:!1,alternativeBrand:null,alternativeModel:null,inquiryNumber:"INQ202401003",inquiryTime:new Date("2024-01-20T09:45:00"),deadline:new Date("2024-01-27T18:00:00"),status:"inquiring"},{id:10,name:"菲尼克斯继电器",model:"REL-MR-24DC/21",brand:"菲尼克斯",category:"电气元件",quantity:2,unitPrice:1600,expectedDate:new Date("2024-04-10"),acceptAlternative:!0,alternativeBrand:"魏德米勒",alternativeModel:"TRZ 24VDC 1CO",inquiryNumber:"INQ202401004",inquiryTime:new Date("2024-01-12T16:00:00"),deadline:new Date("2024-01-19T18:00:00"),status:"expired"}]),I=V(()=>{let n=M.value;if(p.value){const e=p.value.toLowerCase();n=n.filter(a=>a.number.toLowerCase().includes(e))}return l.value.inquiryNumber&&(n=n.filter(e=>e.number.toLowerCase().includes(l.value.inquiryNumber.toLowerCase()))),l.value.inquiryTimeStart&&(n=n.filter(e=>new Date(e.createdTime)>=new Date(l.value.inquiryTimeStart))),l.value.inquiryTimeEnd&&(n=n.filter(e=>new Date(e.createdTime)<=new Date(l.value.inquiryTimeEnd+"T23:59:59"))),l.value.deadlineStart&&(n=n.filter(e=>new Date(e.deadline)>=new Date(l.value.deadlineStart))),l.value.deadlineEnd&&(n=n.filter(e=>new Date(e.deadline)<=new Date(l.value.deadlineEnd+"T23:59:59"))),n}),P=V(()=>{let n=D.value;if(f.value&&f.value!=="all"&&(n=n.filter(e=>e.status===f.value)),p.value){const e=p.value.toLowerCase();n=n.filter(a=>a.model.toLowerCase().includes(e)||a.brand.toLowerCase().includes(e)||a.inquiryNumber.toLowerCase().includes(e)||a.name.toLowerCase().includes(e))}if(l.value.model&&(n=n.filter(e=>e.model.toLowerCase().includes(l.value.model.toLowerCase()))),l.value.brand&&(n=n.filter(e=>e.brand.toLowerCase().includes(l.value.brand.toLowerCase()))),l.value.acceptAlternative!==""){const e=l.value.acceptAlternative==="true";n=n.filter(a=>a.acceptAlternative===e)}return l.value.inquiryNumber&&(n=n.filter(e=>e.inquiryNumber.toLowerCase().includes(l.value.inquiryNumber.toLowerCase()))),l.value.inquiryTimeStart&&(n=n.filter(e=>new Date(e.inquiryTime)>=new Date(l.value.inquiryTimeStart))),l.value.inquiryTimeEnd&&(n=n.filter(e=>new Date(e.inquiryTime)<=new Date(l.value.inquiryTimeEnd+"T23:59:59"))),l.value.deadlineStart&&(n=n.filter(e=>new Date(e.deadline)>=new Date(l.value.deadlineStart))),l.value.deadlineEnd&&(n=n.filter(e=>new Date(e.deadline)<=new Date(l.value.deadlineEnd+"T23:59:59"))),n}),O=()=>{_.back()},x=()=>{_.push("/inquiry")},R=()=>{},z=()=>{p.value=""},H=()=>{l.value={model:"",brand:"",acceptAlternative:"",inquiryNumber:"",inquiryTimeStart:"",inquiryTimeEnd:"",deadlineStart:"",deadlineEnd:""}},X=()=>{q.value=!1},G=n=>({inquiring:"#1890ff",adopted:"#52c41a",cancelled:"#d9d9d9",expired:"#ff4d4f"})[n]||"#d9d9d9",K=n=>({inquiring:"询价中",adopted:"已采纳",cancelled:"已取消",expired:"已截止"})[n]||"未知",T=(n,e="YYYY-MM-DD HH:mm")=>{if(!n)return"";const a=new Date(n);return e==="YYYY-MM-DD"?a.getFullYear()+"-"+String(a.getMonth()+1).padStart(2,"0")+"-"+String(a.getDate()).padStart(2,"0"):a.getFullYear()+"-"+String(a.getMonth()+1).padStart(2,"0")+"-"+String(a.getDate()).padStart(2,"0")+" "+String(a.getHours()).padStart(2,"0")+":"+String(a.getMinutes()).padStart(2,"0")},Z=n=>{h("convert","转换为采购单",`确定要将询价单 #${n.number} 转换为采购单吗？`,()=>{console.log("转换采购单:",n),alert(`询价单 #${n.number} 已转换为采购单`)})},J=n=>{console.log("分享询价单:",n),_.push(`/inquiry-share/${n.number}`)},W=n=>{console.log("邀请报价:",n),alert(`已发送邀请，询价单：#${n.number}`)},j=n=>{console.log("编辑询价单:",n),_.push(`/inquiry?edit=${n.id}`)},ee=n=>{h("delete","删除询价单",`确定要删除询价单 #${n.number} 吗？删除后无法恢复。`,()=>{const e=M.value.findIndex(a=>a.id===n.id);e>-1&&M.value.splice(e,1),D.value=D.value.filter(a=>a.inquiryNumber!==n.number),console.log("删除询价单:",n)})},te=n=>{h("convert","转换为采购单",`确定要将物料"${n.name}"转换为采购单吗？`,()=>{console.log("转换采购单:",n),alert(`物料"${n.name}"已转换为采购单`)})},ne=n=>{console.log("编辑物料:",n),_.push(`/inquiry?edit=${n.inquiryNumber}&material=${n.id}`)},ie=n=>{h("delete","删除物料",`确定要删除物料"${n.name}"吗？删除后无法恢复。`,()=>{const e=D.value.findIndex(a=>a.id===n.id);e>-1&&D.value.splice(e,1),console.log("删除物料:",n)})},ae=n=>{E.value=n,N.value=!0},le=n=>{console.log("采纳报价:",n),alert(`已采纳 ${n.supplierName} 的报价：¥${n.totalPrice.toLocaleString()}`)},se=n=>{console.log("联系供应商:",n),alert(`正在联系供应商：${n.supplierName}`)},h=(n,e,a,A)=>{k.value=n,Q.value=e,B.value=a,S.value=A,w.value=!0},oe=()=>{S.value&&S.value(),w.value=!1};return de(()=>{console.log("询价管理页面已加载")}),(n,e)=>{const a=Y("font-awesome-icon"),A=Y("nut-popup");return r(),d("div",me,[t("div",ye,[t("div",be,[s(a,{icon:"arrow-left",size:"lg",color:"#2a2a35",onClick:O,class:"back-icon"})]),e[15]||(e[15]=t("div",{class:"header-center"},[t("h2",{class:"page-title"},"询价管理")],-1)),t("div",fe,[s(a,{icon:"plus",size:"lg",color:"#f94c30",onClick:x,class:"add-icon"})])]),t("div",ge,[t("div",_e,[t("div",we,[t("div",qe,[s(a,{icon:"search",class:"search-icon"}),v(t("input",{"onUpdate:modelValue":e[0]||(e[0]=i=>p.value=i),type:"text",placeholder:"搜索物料型号、品牌、询价单号...",class:"search-input",onInput:R},null,544),[[m,p.value]]),p.value?(r(),d("button",{key:0,onClick:z,class:"clear-btn"},[s(a,{icon:"times"})])):C("",!0)]),t("button",{onClick:e[1]||(e[1]=i=>q.value=!q.value),class:"advanced-filter-toggle"},[s(a,{icon:"filter"}),e[16]||(e[16]=t("span",null,"高级筛选",-1)),s(a,{icon:q.value?"chevron-up":"chevron-down",class:"toggle-icon"},null,8,["icon"])])]),t("div",{class:g(["advanced-filters",{expanded:q.value}])},[t("div",De,[t("div",Te,[e[17]||(e[17]=t("label",null,"物料型号",-1)),v(t("input",{"onUpdate:modelValue":e[2]||(e[2]=i=>l.value.model=i),type:"text",placeholder:"输入型号"},null,512),[[m,l.value.model]])]),t("div",Ce,[e[18]||(e[18]=t("label",null,"品牌",-1)),v(t("input",{"onUpdate:modelValue":e[3]||(e[3]=i=>l.value.brand=i),type:"text",placeholder:"输入品牌"},null,512),[[m,l.value.brand]])]),t("div",he,[e[20]||(e[20]=t("label",null,"接受平替",-1)),v(t("select",{"onUpdate:modelValue":e[4]||(e[4]=i=>l.value.acceptAlternative=i)},e[19]||(e[19]=[t("option",{value:""},"全部",-1),t("option",{value:"true"},"是",-1),t("option",{value:"false"},"否",-1)]),512),[[re,l.value.acceptAlternative]])])]),t("div",ke,[t("div",Se,[e[21]||(e[21]=t("label",null,"询价单号",-1)),v(t("input",{"onUpdate:modelValue":e[5]||(e[5]=i=>l.value.inquiryNumber=i),type:"text",placeholder:"输入单号"},null,512),[[m,l.value.inquiryNumber]])]),t("div",Ne,[e[22]||(e[22]=t("label",null,"询价时间",-1)),v(t("input",{"onUpdate:modelValue":e[6]||(e[6]=i=>l.value.inquiryTimeStart=i),type:"date"},null,512),[[m,l.value.inquiryTimeStart]]),e[23]||(e[23]=t("span",{class:"date-separator"},"至",-1)),v(t("input",{"onUpdate:modelValue":e[7]||(e[7]=i=>l.value.inquiryTimeEnd=i),type:"date"},null,512),[[m,l.value.inquiryTimeEnd]])])]),t("div",Me,[t("div",xe,[e[24]||(e[24]=t("label",null,"截止时间",-1)),v(t("input",{"onUpdate:modelValue":e[8]||(e[8]=i=>l.value.deadlineStart=i),type:"date"},null,512),[[m,l.value.deadlineStart]]),e[25]||(e[25]=t("span",{class:"date-separator"},"至",-1)),v(t("input",{"onUpdate:modelValue":e[9]||(e[9]=i=>l.value.deadlineEnd=i),type:"date"},null,512),[[m,l.value.deadlineEnd]])]),t("div",Ae,[t("button",{onClick:H,class:"filter-btn reset"},[s(a,{icon:"undo"}),e[26]||(e[26]=y(" 重置 "))]),t("button",{onClick:X,class:"filter-btn apply"},[s(a,{icon:"search"}),e[27]||(e[27]=y(" 应用筛选 "))])])])],2)]),t("div",$e,[t("div",Le,[t("div",{class:g(["view-tab",{active:b.value==="order"}]),onClick:e[10]||(e[10]=i=>b.value="order")},[s(a,{icon:"file-invoice"}),e[28]||(e[28]=t("span",null,"单据视图",-1))],2),t("div",{class:g(["view-tab",{active:b.value==="material"}]),onClick:e[11]||(e[11]=i=>b.value="material")},[s(a,{icon:"boxes"}),e[29]||(e[29]=t("span",null,"物料视图",-1))],2)]),b.value==="material"?(r(),d("div",Qe,[t("div",Be,[(r(!0),d($,null,L(F.value,i=>(r(),d("div",{key:i.value,class:g(["status-chip",{active:f.value===i.value}]),onClick:c=>f.value=f.value===i.value?null:i.value},[t("span",{class:"status-dot",style:U({backgroundColor:i.color})},null,4),t("span",null,o(i.label),1)],10,Ee))),128))])])):C("",!0)]),b.value==="order"?(r(),d("div",Ie,[I.value.length===0?(r(),d("div",Pe,[t("div",Ve,[s(a,{icon:"inbox",size:"3x"})]),e[31]||(e[31]=t("h3",null,"暂无询价单据",-1)),e[32]||(e[32]=t("p",null,"还没有创建任何询价单据",-1)),t("button",{class:"create-btn",onClick:x},[s(a,{icon:"plus"}),e[30]||(e[30]=y(" 创建询价单 "))])])):(r(),d("div",Ye,[(r(!0),d($,null,L(I.value,i=>(r(),d("div",{key:i.id,class:"order-item"},[t("div",Ue,[t("div",Fe,[t("h3",Oe,"询价单 #"+o(i.number),1),t("div",Re,[t("span",ze,o(T(i.createdTime)),1),e[33]||(e[33]=t("span",{class:"order-divider"},"|",-1)),t("span",He,"截止："+o(T(i.deadline)),1)])]),e[34]||(e[34]=t("div",{class:"order-status"},null,-1))]),t("div",Xe,[t("div",Ge,[t("div",Ke,[s(a,{icon:"layer-group"}),t("span",null,o(i.materialTypeCount)+" 种物料",1)]),t("div",Ze,[s(a,{icon:"cubes"}),t("span",null,o(i.totalQuantity)+" 件",1)]),t("div",Je,[s(a,{icon:"yuan-sign"}),t("span",null,"¥"+o(i.totalAmount.toLocaleString()),1)])])]),t("div",We,[t("div",je,[t("button",{class:"action-btn primary",onClick:c=>Z(i)},[s(a,{icon:"shopping-cart"}),e[35]||(e[35]=y(" 转采购单 "))],8,et),t("button",{class:"action-btn",onClick:c=>J(i)},[s(a,{icon:"share"}),e[36]||(e[36]=y(" 分享 "))],8,tt),t("button",{class:"action-btn",onClick:c=>W(i)},[s(a,{icon:"user-plus"}),e[37]||(e[37]=y(" 邀请报价 "))],8,nt)]),t("div",it,[t("button",{class:"action-btn edit",onClick:c=>j(i)},[s(a,{icon:"edit"})],8,at),t("button",{class:"action-btn delete",onClick:c=>ee(i)},[s(a,{icon:"trash"})],8,lt)])])]))),128))]))])):C("",!0),b.value==="material"?(r(),d("div",st,[P.value.length===0?(r(),d("div",ot,[t("div",dt,[s(a,{icon:"inbox",size:"3x"})]),e[39]||(e[39]=t("h3",null,"暂无询价物料",-1)),e[40]||(e[40]=t("p",null,"还没有任何物料在询价中",-1)),t("button",{class:"create-btn",onClick:x},[s(a,{icon:"plus"}),e[38]||(e[38]=y(" 创建询价单 "))])])):(r(),d("div",rt,[(r(!0),d($,null,L(P.value,i=>(r(),d("div",{key:i.id,class:"material-item",onClick:c=>ae(i)},[t("div",ct,[t("div",vt,[t("h3",pt,o(i.name),1),t("div",mt,[t("span",yt,o(i.model),1),e[41]||(e[41]=t("span",{class:"material-divider"},"|",-1)),t("span",bt,o(i.brand),1),e[42]||(e[42]=t("span",{class:"material-divider"},"|",-1)),t("span",ft,o(i.category),1)])]),t("div",gt,[t("span",{class:"status-badge small",style:U({backgroundColor:G(i.status)})},o(K(i.status)),5)])]),t("div",_t,[t("div",wt,[t("div",qt,[t("div",Dt,[s(a,{icon:"calendar"}),t("span",null,o(T(i.expectedDate,"YYYY-MM-DD")),1)]),t("div",Tt,[s(a,{icon:"cube"}),t("span",null,o(i.quantity)+" 件",1)]),t("div",Ct,[s(a,{icon:"yuan-sign"}),t("span",null,"¥"+o(i.unitPrice)+"/件",1)]),t("div",ht,[s(a,{icon:"calculator"}),t("span",null,"总价：¥"+o((i.unitPrice*i.quantity).toLocaleString()),1)])])]),t("div",kt,[t("div",St,[t("div",{class:g(["detail-tag",{alternative:i.acceptAlternative}])},[s(a,{icon:i.acceptAlternative?"check-circle":"times-circle"},null,8,["icon"]),t("span",null,o(i.acceptAlternative?"接受平替":"不接受平替"),1)],2),i.alternativeBrand?(r(),d("div",Nt,[s(a,{icon:"exchange-alt"}),t("span",null,"平替："+o(i.alternativeBrand)+" "+o(i.alternativeModel),1)])):C("",!0)])]),t("div",Mt,[t("div",xt,[t("span",At,"询价单：#"+o(i.inquiryNumber),1),t("span",$t,"询价时间："+o(T(i.inquiryTime)),1),t("span",Lt,"截止时间："+o(T(i.deadline)),1)])])]),t("div",Qt,[t("div",Bt,[t("button",{class:"action-btn primary",disabled:i.status==="cancelled"||i.status==="expired",onClick:c=>te(i)},[s(a,{icon:"shopping-cart"}),e[43]||(e[43]=y(" 转采购单 "))],8,Et)]),t("div",It,[t("button",{class:"action-btn edit",onClick:c=>ne(i)},[s(a,{icon:"edit"})],8,Pt),t("button",{class:"action-btn delete",onClick:c=>ie(i)},[s(a,{icon:"trash"})],8,Vt)])])],8,ut))),128))]))])):C("",!0)]),s(A,{visible:w.value,"onUpdate:visible":e[13]||(e[13]=i=>w.value=i),position:"center",round:""},{default:ue(()=>[t("div",Yt,[t("div",Ut,[s(a,{icon:k.value==="delete"?"exclamation-triangle":"question-circle"},null,8,["icon"]),t("h3",null,o(Q.value),1)]),t("div",Ft,[t("p",null,o(B.value),1)]),t("div",Ot,[t("button",{class:"confirm-btn cancel",onClick:e[12]||(e[12]=i=>w.value=!1)}," 取消 "),t("button",{class:g(["confirm-btn",k.value]),onClick:oe}," 确认 ",2)])])]),_:1},8,["visible"]),s(ve,{visible:N.value,"onUpdate:visible":e[14]||(e[14]=i=>N.value=i),"material-data":E.value,onAdoptQuotation:le,onContactSupplier:se},null,8,["visible","material-data"])])}}},Kt=pe(Rt,[["__scopeId","data-v-246aefe9"]]);export{Kt as default};
